"use client"

import { motion } from "framer-motion"
import { Briefcase, GraduationCap, Calendar, MapPin } from "lucide-react"
import { cn } from "@/lib/utils"

interface TimelineCardProps {
  item: {
    id: number
    type: "work" | "education"
    title: string
    org: string
    time: string
    description: string
  }
  index: number
  isLeft?: boolean
}

export default function TimelineCard({ item, index, isLeft = false }: TimelineCardProps) {
  const Icon = item.type === "work" ? Briefcase : GraduationCap
  
  return (
    <motion.div
      initial={{ opacity: 0, x: isLeft ? -50 : 50 }}
      whileInView={{ opacity: 1, x: 0 }}
      viewport={{ once: true, amount: 0.3 }}
      transition={{ duration: 0.6, delay: index * 0.1 }}
      className={cn(
        "relative group",
        isLeft ? "text-right" : "text-left"
      )}
    >
      {/* Card */}
      <div className={cn(
        "relative p-6 rounded-2xl border border-gray-800/50 bg-gray-900/50 backdrop-blur-sm",
        "hover:border-blue-500/30 hover:bg-gray-800/50 transition-all duration-300",
        "hover-lift glass-dark"
      )}>
        {/* Header */}
        <div className={cn(
          "flex items-center gap-3 mb-4",
          isLeft ? "flex-row-reverse" : "flex-row"
        )}>
          <div className={cn(
            "p-2 rounded-lg",
            item.type === "work" 
              ? "bg-blue-500/20 text-blue-400" 
              : "bg-purple-500/20 text-purple-400"
          )}>
            <Icon size={20} />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-white group-hover:text-blue-400 transition-colors">
              {item.title}
            </h3>
            <p className="text-gray-300 font-medium">{item.org}</p>
          </div>
        </div>

        {/* Time & Location */}
        <div className={cn(
          "flex items-center gap-4 mb-4 text-sm text-gray-400",
          isLeft ? "justify-end" : "justify-start"
        )}>
          <div className="flex items-center gap-1">
            <Calendar size={14} />
            <span>{item.time}</span>
          </div>
        </div>

        {/* Description */}
        <p className="text-gray-300 leading-relaxed">
          {item.description}
        </p>

        {/* Decorative elements */}
        <div className={cn(
          "absolute top-6 w-1 h-12 bg-gradient-to-b rounded-full",
          item.type === "work" 
            ? "from-blue-500 to-blue-600" 
            : "from-purple-500 to-purple-600",
          isLeft ? "right-0 translate-x-1/2" : "left-0 -translate-x-1/2"
        )} />

        {/* Glow effect on hover */}
        <div className={cn(
          "absolute inset-0 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none",
          "bg-gradient-to-r from-blue-500/5 to-purple-500/5"
        )} />
      </div>

      {/* Connection line to timeline */}
      <div className={cn(
        "absolute top-8 w-8 h-0.5 bg-gray-700 group-hover:bg-blue-500/50 transition-colors",
        isLeft ? "right-0 translate-x-full" : "left-0 -translate-x-full"
      )} />
    </motion.div>
  )
}

// Enhanced Timeline Section Component
interface EnhancedTimelineProps {
  timeline: Array<{
    id: number
    type: "work" | "education"
    title: string
    org: string
    time: string
    description: string
  }>
}

export function EnhancedTimeline({ timeline }: EnhancedTimelineProps) {
  if (!timeline || timeline.length === 0) return null

  return (
    <div className="relative max-w-6xl mx-auto">
      {/* Central timeline line */}
      <div className="absolute left-1/2 top-0 bottom-0 w-0.5 bg-gradient-to-b from-blue-500/50 via-purple-500/50 to-pink-500/50 transform -translate-x-1/2" />
      
      {/* Timeline items */}
      <div className="space-y-12">
        {timeline.map((item, index) => {
          const isLeft = index % 2 === 0
          
          return (
            <div key={item.id} className="relative">
              {/* Timeline dot */}
              <motion.div
                initial={{ scale: 0, opacity: 0 }}
                whileInView={{ scale: 1, opacity: 1 }}
                viewport={{ once: true }}
                transition={{ duration: 0.5, delay: index * 0.1 + 0.2 }}
                className="absolute left-1/2 top-8 w-4 h-4 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transform -translate-x-1/2 z-10 shadow-lg"
              >
                <div className="absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-ping opacity-30" />
              </motion.div>
              
              {/* Card positioned left or right */}
              <div className={cn(
                "w-5/12",
                isLeft ? "ml-0" : "ml-auto"
              )}>
                <TimelineCard 
                  item={item} 
                  index={index} 
                  isLeft={isLeft} 
                />
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}
