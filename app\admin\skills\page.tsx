"use client";

import { useState, useEffect } from "react";
import { usePortfolio } from "@/contexts/portfolio-context";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { toast } from "@/components/ui/use-toast";
import { Toaster } from "@/components/ui/toaster";
import { X, Plus } from "lucide-react";

export default function SkillsPage() {
  const { data, updateSkills } = usePortfolio();
  const [skills, setSkills] = useState(data.skills || []);
  const [newSkill, setNewSkill] = useState({ name: "", percentage: 0, color: "bg-blue-500" });
  const [isSaving, setIsSaving] = useState(false);

  // Update local skills when data changes
  useEffect(() => {
    setSkills(data.skills || []);
  }, [data.skills]);

  const handleAddSkill = () => {
    if (!newSkill.name.trim() || newSkill.percentage < 0 || newSkill.percentage > 100) return;
    setSkills([...skills, { ...newSkill }]);
    setNewSkill({ name: "", percentage: 0, color: "bg-blue-500" });
  };

  const handleRemoveSkill = (name: string) => {
    setSkills(skills.filter((s: any) => s.name !== name));
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewSkill((prev) => ({ ...prev, [name]: name === "percentage" ? Number(value) : value }));
  };

  const handleColorChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setNewSkill((prev) => ({ ...prev, color: e.target.value }));
  };

  const handleSave = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    try {
      await updateSkills(skills);
      toast({ title: "Skills updated", description: "Your skills have been updated successfully." });
    } catch (error) {
      console.error("Error updating skills:", error);
      toast({ title: "Error", description: "Failed to update skills.", variant: "destructive" });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-bold text-white">Skills & Expertise</h1>
      <Card className="bg-gray-900 border-gray-800 bg-gray-900 border-gray-800">
        <form onSubmit={handleSave}>
          <CardHeader>
            <CardTitle className="text-white text-white">Edit Skills & Expertise</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex gap-2">
              <Input name="name" value={newSkill.name} onChange={handleChange} placeholder="Skill name" />
              <Input name="percentage" type="number" min={0} max={100} value={newSkill.percentage} onChange={handleChange} placeholder="%" className="w-24" />
              <select value={newSkill.color} onChange={handleColorChange} className="border rounded px-2 py-1 bg-gray-800 border-gray-700 text-white focus:border-blue-500">
                <option value="bg-blue-500">Blue</option>
                <option value="bg-green-500">Green</option>
                <option value="bg-purple-500">Purple</option>
                <option value="bg-orange-500">Orange</option>
                <option value="bg-pink-500">Pink</option>
                <option value="bg-yellow-500">Yellow</option>
              </select>
              <Button type="button" onClick={handleAddSkill}>
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2 mt-2">
              {skills.map((skill: any) => (
                <span key={skill.name} className={`inline-flex items-center px-3 py-1 rounded-full text-white ${skill.color}`}>
                  {skill.name} ({skill.percentage}%)
                  <button type="button" className="ml-2 text-white/80 hover:text-red-200" onClick={() => handleRemoveSkill(skill.name)}>
                    <X className="h-3 w-3" />
                  </button>
                </span>
              ))}
            </div>
          </CardContent>
          <CardFooter>
            <Button type="submit" disabled={isSaving}>
              {isSaving ? "Saving..." : "Save Changes"}
            </Button>
          </CardFooter>
        </form>
      </Card>
      <Toaster />
    </div>
  );
}