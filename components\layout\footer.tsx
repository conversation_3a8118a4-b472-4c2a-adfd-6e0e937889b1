import SocialIcon from "@/components/ui/social-icon"
import { type SocialLink, type PersonalInfo } from "@/actions/portfolio-actions"

interface FooterProps {
  personalInfo: PersonalInfo
  socialLinks: SocialLink[]
}

export default function Footer({ personalInfo, socialLinks }: FooterProps) {
  return (
    <footer className="py-12 px-6 md:px-12 lg:px-24 border-t border-gray-800 bg-gray-950 text-gray-400">
      <div className="max-w-7xl mx-auto flex flex-col md:flex-row justify-between items-center">
        <p className="mb-4 md:mb-0">
          © {new Date().getFullYear()}{" "}
          <span className="text-white font-semibold">{personalInfo.name}</span> •{" "}
          {personalInfo.location} •{" "}
          <a
            href={`https://${personalInfo.website}`}
            className="hover:underline text-blue-400"
          >
            {personalInfo.website}
          </a>
        </p>
        <div className="flex gap-6">
          {socialLinks.map((link) => (
            <a
              key={link.platform}
              href={link.url}
              className="text-gray-400 hover:text-rose-400 transition-colors"
              target="_blank"
              rel="noopener noreferrer"
            >
              <span className="sr-only">{link.platform}</span>
              <SocialIcon platform={link.platform} />
            </a>
          ))}
        </div>
      </div>
    </footer>
  )
}
