"use client"

import { Briefcase, GraduationCap, Sparkles } from "lucide-react"
import { GradientText } from "@/components/animate-ui/text/gradient"
import { ShimmeringText } from "@/components/animate-ui/text/shimmering"
import { motion } from "framer-motion"

export interface TimelineItem {
  id: number
  type: "work" | "education"
  title: string
  org: string
  time: string
  description: string
}

interface TimelineSectionProps {
  timeline: TimelineItem[]
}

export default function TimelineSection({ timeline }: TimelineSectionProps) {
  if (!timeline || timeline.length === 0) return null
  return (
    <motion.section
      id="projects"
      initial={{ opacity: 0, y: 40 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.2 }}
      transition={{ duration: 0.7, ease: "easeOut" }}
      className="relative py-24 bg-gray-950 text-white"
    >
      <div className="container mx-auto px-4 md:px-12 lg:px-32">
        <h2 className="text-3xl md:text-4xl font-extrabold mb-16 text-center flex items-center justify-center gap-2">
          <Sparkles className="inline-block text-yellow-400 animate-pulse" />
          <GradientText text="Development Journey" />
        </h2>
        <div className="relative">
          <div className="absolute left-1/2 top-0 bottom-0 w-1 bg-gradient-to-b from-blue-900 via-purple-900 to-pink-900 rounded-full opacity-30 -translate-x-1/2" />
          <ul className="relative z-10 space-y-16">
            {timeline.map((item, idx) => (
              <motion.li
                key={item.id}
                className={`relative flex items-center group ${idx % 2 === 0 ? '' : 'flex-row-reverse'}`}
                style={{ minHeight: 160 }}
                initial={{ opacity: 0, y: 40 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, amount: 0.2 }}
                transition={{ duration: 0.6, delay: idx * 0.08, ease: 'easeOut' }}
              >
                <div className={`w-1/2 flex ${idx % 2 === 0 ? "pr-8 justify-end" : "pl-8 justify-start"}`}>
                  <motion.div
                    className="bg-gray-900/90 backdrop-blur-lg shadow-xl rounded-2xl p-6 transition-transform duration-300 group-hover:scale-105 group-hover:shadow-2xl border-0"
                    initial={{ opacity: 0, scale: 0.95 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: idx * 0.12 + 0.1, ease: 'easeOut' }}
                    viewport={{ once: true, amount: 0.2 }}
                  >
                    <div className="flex items-center gap-3 mb-2">
                      {item.type === "work" ? (
                        <Briefcase className="w-6 h-6 text-blue-400" />
                      ) : (
                        <GraduationCap className="w-6 h-6 text-purple-400" />
                      )}
                      <ShimmeringText text={item.title} />
                    </div>
                    <div className="text-md font-semibold text-gray-200 mb-1">{item.org}</div>
                    <div className="text-sm text-gray-400 mb-2">{item.time}</div>
                    <div className="text-gray-300">{item.description}</div>
                  </motion.div>
                </div>
                {/* Dot on timeline - fix centering, using flex and relative */}
                <motion.div
                  className="flex flex-col items-center justify-center h-full z-20"
                  style={{ minHeight: 160 }}
                  initial={{ scale: 0, opacity: 0 }}
                  whileInView={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.5, delay: idx * 0.13 + 0.2, ease: 'easeOut' }}
                  viewport={{ once: true, amount: 0.2 }}
                >
                  <div
                    className="relative w-10 h-10 bg-gradient-to-tr from-blue-800 via-purple-800 to-pink-800 rounded-full flex items-center justify-center shadow-lg border-4 border-gray-950 animate-pulse-slow"
                  >
                    <span className="block w-4 h-4 bg-white rounded-full animate-pulse"></span>
                  </div>
                </motion.div>
                <div className="w-1/2"></div>
              </motion.li>
            ))}
          </ul>
        </div>
      </div>
    </motion.section>
  )
}

// Nếu cần thêm animation Tailwind, hãy bổ sung vào tailwind.config.ts:
// theme: { extend: { animation: { 'fade-in-up': 'fadeInUp 0.7s cubic-bezier(.39,.575,.565,1) both', 'pulse-slow': 'pulse 2s cubic-bezier(.4,0,.6,1) infinite' }, keyframes: { fadeInUp: { '0%': { opacity: 0, transform: 'translateY(60px) scale(0.95)' }, '100%': { opacity: 1, transform: 'translateY(0) scale(1)' } } } } } }