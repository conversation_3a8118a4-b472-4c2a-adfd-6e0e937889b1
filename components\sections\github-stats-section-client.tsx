"use client"

import { Github } from "lucide-react"
import StatCard from "@/components/ui/stat-card"
import { motion } from "framer-motion"

export type GithubStats = {
  stars: number;
  repos: number;
  followers: number;
}

interface GithubStatsSectionProps {
  githubStats: GithubStats
}

export default function GithubStatsSectionClient({ githubStats }: GithubStatsSectionProps) {
  return (
    <section className="py-16 px-6 md:px-12 lg:px-24 max-w-7xl mx-auto bg-gradient-to-br from-gray-900 via-gray-950 to-purple-950 text-white rounded-3xl shadow-2xl border-none">
      <div className="bg-gradient-to-br from-[#181c2b] via-[#232946] to-[#2d3250] rounded-3xl p-1">
        <div className="bg-[#181c2b]/95 rounded-2xl p-8">
          <h2 className="text-3xl font-bold mb-8 flex items-center text-blue-200 drop-shadow-lg">
            <Github className="mr-2 h-7 w-7 text-blue-400" /> GitHub Stats
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.2 }}
              transition={{ duration: 0.6, delay: 0 * 0.12, ease: 'easeOut' }}
            >
              <StatCard
                title="Stars"
                value={githubStats.stars}
                description="Total stars earned"
                className="bg-[#232946] border border-blue-700/40 text-blue-100 shadow-xl hover:shadow-blue-700/30 transition-all duration-300"
                iconColor="text-yellow-300"
              />
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.2 }}
              transition={{ duration: 0.6, delay: 1 * 0.12, ease: 'easeOut' }}
            >
              <StatCard
                title="Repositories"
                value={githubStats.repos}
                description="Public repositories"
                className="bg-[#232946] border border-purple-700/40 text-purple-100 shadow-xl hover:shadow-purple-700/30 transition-all duration-300"
                iconColor="text-pink-300"
              />
            </motion.div>
            <motion.div
              initial={{ opacity: 0, y: 40 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, amount: 0.2 }}
              transition={{ duration: 0.6, delay: 2 * 0.12, ease: 'easeOut' }}
            >
              <StatCard
                title="Followers"
                value={githubStats.followers}
                description="GitHub followers"
                className="bg-[#232946] border border-pink-700/40 text-pink-100 shadow-xl hover:shadow-pink-700/30 transition-all duration-300"
                iconColor="text-blue-300"
              />
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )
}
