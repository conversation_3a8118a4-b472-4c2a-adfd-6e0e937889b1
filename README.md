# Portfolio Admin System

A simple portfolio management system with local JSON storage and image uploads.

## Features

- **Local JSON Storage**: All data is stored in `data/portfolio.json`
- **Local Image Upload**: Images are uploaded to `public/images/` directory
- **Simple Authentication**: Basic local authentication system
- **Admin Dashboard**: Clean interface to manage portfolio content
- **Responsive Design**: Works on desktop and mobile devices

## Getting Started

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Run the development server**:
   ```bash
   npm run dev
   ```

3. **Access the admin panel**:
   - Go to `http://localhost:3000/admin/login`
   - Use the demo credentials:
     - Email: `<EMAIL>`
     - Password: `admin123`

## Project Structure

```
├── app/
│   ├── admin/           # Admin panel pages
│   ├── api/             # API routes
│   └── page.tsx         # Public portfolio page
├── components/          # Reusable components
├── contexts/            # React contexts
├── data/
│   └── portfolio.json   # Portfolio data storage
├── public/
│   └── images/          # Uploaded images
├── utils/               # Utility functions
└── actions/             # Server actions
```

## Data Management

### Portfolio Data
All portfolio data is stored in `data/portfolio.json` with the following structure:

```json
{
  "personalInfo": {
    "name": "Your Name",
    "role": "Your Role",
    "bio": "Your bio...",
    "location": "Your Location",
    "website": "https://yourwebsite.com",
    "email": "<EMAIL>",
    "profileImage": "/images/avatar.jpg"
  },
  "technologies": ["JavaScript", "React", "Next.js"],
  "projects": [
    {
      "id": "project-1",
      "title": "Project Title",
      "description": "Project description...",
      "tags": ["React", "TypeScript"],
      "imageUrl": "/images/project-1.jpg"
    }
  ],
  "socialLinks": [
    {
      "id": "github",
      "platform": "GitHub",
      "url": "https://github.com/username"
    }
  ],
  "githubStats": {
    "stars": 50,
    "repos": 25,
    "followers": 100
  }
}
```

### Image Storage
- Images are uploaded to the `public/images/` directory
- Avatar images are saved as `avatar.{extension}`
- Project images are saved as `project-{id}.{extension}`
- Supported formats: JPG, PNG, GIF, WebP
- Maximum file size: 5MB

## Admin Features

### Personal Information
- Update name, role, bio, location, website, and email
- Upload and manage profile image

### Technologies
- Add, edit, and remove technologies from your tech stack

### Projects
- Create new projects with images and tags
- Edit existing projects
- Delete projects
- Upload project images

### Social Links
- Manage social media links
- Support for various platforms

### GitHub Stats
- Update GitHub statistics manually
- Display stars, repositories, and followers

## Authentication

The system uses a simple local authentication mechanism:
- Credentials are stored in the auth context
- Session is maintained in localStorage
- Default admin credentials: `<EMAIL>` / `admin123`

**Note**: This is a demo authentication system. For production use, implement proper server-side authentication.

## Customization

### Styling
The project uses Tailwind CSS for styling. You can customize the appearance by modifying the Tailwind classes in the components.

### Adding New Features
1. Create new components in the `components/` directory
2. Add new API routes in `app/api/`
3. Update the data structure in `utils/json-storage.ts`
4. Add new admin pages in `app/admin/`

## Deployment

1. **Build the project**:
   ```bash
   npm run build
   ```

2. **Start the production server**:
   ```bash
   npm start
   ```

3. **Deploy to your preferred platform**:
   - Vercel, Netlify, or any Node.js hosting service
   - Make sure to set up file upload permissions for the `public/images/` directory

## Environment Variables

Tạo file `.env.local` ở thư mục gốc với nội dung:

```env
# --- Supabase (nếu dùng) ---
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key

# --- Email (Resend) ---
RESEND_API_KEY=your-resend-api-key
CONTACT_RECEIVER_EMAIL=your-contact-email

# --- GitHub API (cho stats section) ---
GITHUB_USERNAME=your-github-username
GITHUB_TOKEN=your-github-token

# --- Unsplash API (tùy chọn, cho image search) ---
UNSPLASH_ACCESS_KEY=your-unsplash-access-key

# --- Admin credentials (chỉ dùng local/dev, không dùng production) ---
ADMIN_USERNAME=admin
ADMIN_PASSWORD=password
```

**Giải thích:**
- Các biến này sẽ được quản lý tập trung trong file `lib/config.ts` để dễ sử dụng và bảo trì.
- Bạn có thể chỉnh sửa hoặc mở rộng các biến trong `lib/config.ts` nếu muốn gom thêm biến khác.
- Không nên commit file `.env.local` lên git để bảo mật thông tin cá nhân.

**Biến sử dụng ở đâu?**
- `GITHUB_USERNAME`, `GITHUB_TOKEN`: Dùng cho Github Stats (hiển thị số repo, stars, followers...)
- `RESEND_API_KEY`, `CONTACT_RECEIVER_EMAIL`: Dùng cho chức năng gửi email liên hệ
- `UNSPLASH_ACCESS_KEY`: Dùng cho tính năng tìm kiếm ảnh Unsplash (nếu có)
- `ADMIN_USERNAME`, `ADMIN_PASSWORD`: Đăng nhập admin local/dev

- Bạn có thể xem hoặc chỉnh sửa các biến này trong file `lib/config.ts` để dùng lại ở nhiều nơi trong dự án.

## License

This project is open source and available under the MIT License.