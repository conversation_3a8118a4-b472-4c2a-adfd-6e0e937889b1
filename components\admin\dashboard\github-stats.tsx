import { GithubStats } from "@/actions/portfolio-actions";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

interface GitHubStatsProps {
  githubStats: GithubStats;
}

export default function GitHubStats({ githubStats }: GitHubStatsProps) {
  return (
    <>
      <div className="space-y-3">
        <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
          <span className="text-sm font-medium text-gray-300">Stars</span>
          <span className="text-sm text-white">{githubStats.stars}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm font-medium text-gray-300">
            Repositories
          </span>
          <span className="text-sm text-white">{githubStats.repos}</span>
        </div>
        <div className="flex justify-between">
          <span className="text-sm font-medium text-gray-300">Followers</span>
          <span className="text-sm text-white">{githubStats.followers}</span>
        </div>
      </div>
      <Button
        asChild
        className="w-full mt-4 bg-gray-600 hover:bg-gray-700 text-white border-0"
        size="sm"
      >
        <Link href="/admin/github-stats">
          Edit <ArrowRight className="ml-2 h-4 w-4" />
        </Link>
      </Button>
    </>
  );
}
