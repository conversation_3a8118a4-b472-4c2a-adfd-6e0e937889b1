import { type PersonalInfo } from "@/actions/portfolio-actions";

interface PersonalInfoProps {
  personalInfo: PersonalInfo;
}

export default function PersonalInfo({ personalInfo }: PersonalInfoProps) {
  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
        <span className="text-sm font-medium text-gray-300">Name</span>
        <span className="text-sm text-white font-medium">
          {personalInfo.name}
        </span>
      </div>
      <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
        <span className="text-sm font-medium text-gray-300">Location</span>
        <span className="text-sm text-gray-300">
          {personalInfo.location}
        </span>
      </div>
      <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
        <span className="text-sm font-medium text-gray-300">Website</span>
        <span className="text-sm text-blue-400 hover:text-blue-300 transition-colors">
          {personalInfo.website}
        </span>
      </div>
    </div>
  );
}
