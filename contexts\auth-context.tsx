"use client"

import { create<PERSON>ontext, useContext, useState, useEffect, type ReactNode } from "react"

type User = {
  id: string
  email: string
  isAdmin: boolean
} | null

interface AuthContextType {
  user: User
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>
  logout: () => Promise<void>
  isLoading: boolean
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Simple local authentication - in a real app, you'd want proper authentication
const ADMIN_CREDENTIALS = {
  email: "<EMAIL>",
  password: "admin123",
  id: "admin-user-1"
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check if user is already logged in (from localStorage)
    const checkAuthStatus = () => {
      try {
        const savedUser = localStorage.getItem('portfolio-auth-user')
        if (savedUser) {
          setUser(JSON.parse(savedUser))
        }
      } catch (error) {
        console.error('Error checking auth status:', error)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuthStatus()
  }, [])

  const login = async (email: string, password: string) => {
    try {
      // Simple credential check - in a real app, this would be server-side
      if (email === ADMIN_CREDENTIALS.email && password === ADMIN_CREDENTIALS.password) {
        const user = {
          id: ADMIN_CREDENTIALS.id,
          email: ADMIN_CREDENTIALS.email,
          isAdmin: true
        }
        
        setUser(user)
        localStorage.setItem('portfolio-auth-user', JSON.stringify(user))
        
        return { success: true }
      } else {
        return { success: false, error: 'Invalid email or password' }
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  const logout = async () => {
    setUser(null)
    localStorage.removeItem('portfolio-auth-user')
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        login,
        logout,
        isLoading
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider")
  }
  return context
}