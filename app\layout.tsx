import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { <PERSON><PERSON>_Mono } from "next/font/google"
import "./globals.css"
import { PortfolioProvider } from "@/contexts/portfolio-context"
import { ErrorBoundary } from "@/components/error-boundary"
import StructuredData from "@/components/structured-data"
import { cn } from "@/lib/utils"

const robotoMono = Roboto_Mono({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: {
    default: "Truongnat - Software Engineer & Full Stack Developer",
    template: "%s | Truongnat Portfolio"
  },
  description: "Experienced Software Engineer specializing in web and mobile development. Explore my portfolio featuring modern web applications, UI/UX designs, and innovative projects built with React, Next.js, and TypeScript.",
  keywords: [
    "Software Engineer",
    "Full Stack Developer",
    "React Developer",
    "Next.js",
    "TypeScript",
    "Web Development",
    "Mobile Development",
    "UI/UX Design",
    "Portfolio",
    "Truongnat"
  ],
  authors: [{ name: "<PERSON><PERSON><PERSON><PERSON>", url: "https://yourwebsite.com" }],
  creator: "<PERSON>ruong<PERSON>",
  publisher: "Truongnat",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL("https://yourwebsite.com"),
  alternates: {
    canonical: "/",
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://yourwebsite.com",
    title: "Truongnat - Software Engineer & Full Stack Developer",
    description: "Experienced Software Engineer specializing in web and mobile development. Explore my portfolio featuring modern web applications and innovative projects.",
    siteName: "Truongnat Portfolio",
    images: [
      {
        url: "/images/og-image.jpg",
        width: 1200,
        height: 630,
        alt: "Truongnat Portfolio",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Truongnat - Software Engineer & Full Stack Developer",
    description: "Experienced Software Engineer specializing in web and mobile development.",
    images: ["/images/og-image.jpg"],
    creator: "@truongnat",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  verification: {
    google: "your-google-verification-code",
    yandex: "your-yandex-verification-code",
  },
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <head>
        <StructuredData />
      </head>
      <body className={cn(robotoMono.className, 'bg-slate-950 text-slate-50 antialiased')}>
        <div className="relative z-10">
          <ErrorBoundary>
            <PortfolioProvider>
              {children}
            </PortfolioProvider>
          </ErrorBoundary>
        </div>
      </body>
    </html>
  )
}