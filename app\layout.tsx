import type React from "react"
import type { <PERSON><PERSON><PERSON> } from "next"
import { Roboto_Mono } from "next/font/google"
import "./globals.css"
import { PortfolioProvider } from "@/contexts/portfolio-context"
import { cn } from "@/lib/utils"

const robotoMono = Roboto_Mono({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Truongnat - Portfolio",
  description: "Personal portfolio website",
  generator: 'v0.dev'
}

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en">
      <body className={cn(robotoMono.className, 'bg-slate-950 text-slate-50 antialiased')}>
        <div className="relative z-10">
            <PortfolioProvider>
              {children}
            </PortfolioProvider>
        </div>
      </body>
    </html>
  )
}