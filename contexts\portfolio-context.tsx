"use client"

import { create<PERSON>ontext, useContext, useState, useEffect, type ReactNode } from "react"
import {
  fetchPortfolioData,
  updatePersonalInfo as updatePersonalInfoAction,
  updateTechnologies as updateTechnologiesAction,
  createGalleryItem as addGalleryItemAction,
  updateGalleryItem as updateGalleryItemAction,
  deleteGalleryItem as deleteGalleryItemAction,
  updateSocialLinks as updateSocialLinksAction,
  updateGithubStats as updateGithubStatsAction,
  updateSkills as updateSkillsAction,
  updateAboutMe as updateAboutMeAction,
  updateWork<PERSON>reas as updateWorkAreasAction,
  updateTimeline as updateTimelineAction,
  updateServiceCards as updateServiceCardsAction,
  updateStats as updateStatsAction,
  type PersonalInfo,
  type GalleryItem,
  type SocialLink,
  type GithubStats,
  type Skill,
  type TimelineItem,
  type ServiceCard,
  type Stat,
} from "@/actions/portfolio-actions"

interface PortfolioData {
  personalInfo: PersonalInfo
  technologies: string[]
  gallery: GalleryItem[]
  socialLinks: SocialLink[]
  githubStats: GithubStats
  aboutMe: string
  workAreas: string[]
  skills: { name: string; percentage: number; color?: string }[]
  timeline: TimelineItem[]
  serviceCards: ServiceCard[]
  stats: Stat[]
}

interface PortfolioContextType {
  data: PortfolioData
  isLoading: boolean
  error: string | null
  updatePersonalInfo: (info: PersonalInfo) => Promise<{ success: boolean; error?: string }>
  updateTechnologies: (techs: string[]) => Promise<void>
  addGalleryItem: (item: Omit<GalleryItem, "id">) => Promise<string | null>
  updateGalleryItem: (item: GalleryItem) => Promise<void>
  deleteGalleryItem: (id: string) => Promise<void>
  updateSocialLinks: (links: SocialLink[]) => Promise<void>
  updateGithubStats: (stats: GithubStats) => Promise<void>
  updateSkills: (skills: Skill[]) => Promise<void>
  updateAboutMe: (aboutMe: string) => Promise<void>
  updateWorkAreas: (workAreas: string[]) => Promise<void>
  updateTimeline: (timeline: TimelineItem[]) => Promise<void>
  updateServiceCards: (serviceCards: ServiceCard[]) => Promise<void>
  updateStats: (stats: Stat[]) => Promise<void>
  refreshData: () => Promise<void>
}

// Default empty data
const defaultData: PortfolioData = {
  personalInfo: {
    name: "",
    role: "",
    bio: "",
    location: "",
    website: "",
    email: "",
    profileImage: ""
  },
  technologies: [],
  gallery: [],
  socialLinks: [],
  githubStats: {
    stars: 0,
    repos: 0,
    followers: 0
  },
  aboutMe: "",
  workAreas: [],
  skills: [],
  timeline: [],
  serviceCards: [],
  stats: []
}

const PortfolioContext = createContext<PortfolioContextType | undefined>(undefined)

export function PortfolioProvider({ children }: { children: ReactNode }) {
  const [data, setData] = useState<PortfolioData>(defaultData)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const refreshData = async () => {
    try {
      setIsLoading(true)
      setError(null)

      const portfolioData = await fetchPortfolioData()
      setData((prev) => ({
        ...prev,
        ...portfolioData,
        aboutMe: portfolioData.aboutMe ?? prev.aboutMe,
        workAreas: portfolioData.workAreas ?? prev.workAreas,
        skills: portfolioData.skills ?? prev.skills,
        timeline: portfolioData.timeline ?? prev.timeline,
        serviceCards: portfolioData.serviceCards ?? prev.serviceCards,
        stats: portfolioData.stats ?? prev.stats,
      }))
    } catch (err) {
      console.error("Error fetching portfolio data:", err)
      setError("Failed to load portfolio data.")
      setData(defaultData)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    refreshData()
  }, [])

  const updatePersonalInfo = async (info: PersonalInfo) => {
    try {
      const result = await updatePersonalInfoAction(info)

      if (result.success) {
        // Update the state with the new info
        setData((prev) => ({
          ...prev,
          personalInfo: {
            ...info,
            // Ensure we keep any fields that might not be in the form
            profileImage: info.profileImage || prev.personalInfo.profileImage,
          },
        }))

        // Force a refresh of the data
        await refreshData()
      }

      return result
    } catch (err) {
      console.error("Error updating personal info:", err)
      setData((prev) => ({ ...prev, personalInfo: info }))
      throw err
    }
  }

  const updateTechnologies = async (techs: string[]) => {
    try {
      const result = await updateTechnologiesAction(techs)

      if (result.success) {
        setData((prev) => ({ ...prev, technologies: techs }))
      }
    } catch (err) {
      console.error("Error updating technologies:", err)
      setData((prev) => ({ ...prev, technologies: techs }))
      throw err
    }
  }

  const addGalleryItem = async (item: Omit<GalleryItem, "id">) => {
    try {
      const result = await addGalleryItemAction(item)

      if (result.success && result.id) {
        const newItem = {
          ...item,
          id: result.id,
        }

        setData((prev) => ({
          ...prev,
          gallery: [...prev.gallery, newItem],
        }))

        return result.id
      }

      return null
    } catch (err) {
      console.error("Error adding gallery item:", err)
      // Create a local ID and add the item locally
      const localId = `gallery-${Date.now()}`
      const newItem = {
        ...item,
        id: localId,
      }

      setData((prev) => ({
        ...prev,
        gallery: [...prev.gallery, newItem],
      }))

      return localId
    }
  }

  const updateGalleryItem = async (item: GalleryItem) => {
    try {
      const result = await updateGalleryItemAction(item)

      if (result.success) {
        setData((prev) => ({
          ...prev,
          gallery: prev.gallery.map((g) => (g.id === item.id ? item : g)),
        }))
      }
    } catch (err) {
      console.error("Error updating gallery item:", err)
      setData((prev) => ({
        ...prev,
        gallery: prev.gallery.map((g) => (g.id === item.id ? item : g)),
      }))
      throw err
    }
  }

  const deleteGalleryItem = async (id: string) => {
    try {
      const result = await deleteGalleryItemAction(id)

      if (result.success) {
        setData((prev) => ({
          ...prev,
          gallery: prev.gallery.filter((g) => g.id !== id),
        }))
      }
    } catch (err) {
      console.error("Error deleting gallery item:", err)
      setData((prev) => ({
        ...prev,
        gallery: prev.gallery.filter((g) => g.id !== id),
      }))
      throw err
    }
  }

  const updateSocialLinks = async (links: SocialLink[]) => {
    try {
      const result = await updateSocialLinksAction(links)

      if (result.success) {
        setData((prev) => ({
          ...prev,
          socialLinks: links,
        }))
      }
    } catch (err) {
      console.error("Error updating social links:", err)
      setData((prev) => ({
        ...prev,
        socialLinks: links,
      }))
      throw err
    }
  }

  const updateGithubStats = async (stats: GithubStats) => {
    try {
      const result = await updateGithubStatsAction(stats)

      if (result.success) {
        setData((prev) => ({
          ...prev,
          githubStats: stats,
        }))
      }
    } catch (err) {
      console.error("Error updating GitHub stats:", err)
      setData((prev) => ({
        ...prev,
        githubStats: stats,
      }))
      throw err
    }
  }

  const updateSkills = async (skills: Skill[]) => {
    try {
      const result = await updateSkillsAction(skills)

      if (result.success) {
        setData((prev) => ({
          ...prev,
          skills: skills,
        }))
      }
    } catch (err) {
      console.error("Error updating skills:", err)
      setData((prev) => ({
        ...prev,
        skills: skills,
      }))
      throw err
    }
  }

  const updateAboutMe = async (aboutMe: string) => {
    try {
      const result = await updateAboutMeAction(aboutMe)

      if (result.success) {
        setData((prev) => ({
          ...prev,
          aboutMe: aboutMe,
        }))
      }
    } catch (err) {
      console.error("Error updating about me:", err)
      setData((prev) => ({
        ...prev,
        aboutMe: aboutMe,
      }))
      throw err
    }
  }

  const updateWorkAreas = async (workAreas: string[]) => {
    try {
      const result = await updateWorkAreasAction(workAreas)

      if (result.success) {
        setData((prev) => ({
          ...prev,
          workAreas: workAreas,
        }))
      }
    } catch (err) {
      console.error("Error updating work areas:", err)
      setData((prev) => ({
        ...prev,
        workAreas: workAreas,
      }))
      throw err
    }
  }

  const updateTimeline = async (timeline: TimelineItem[]) => {
    try {
      const result = await updateTimelineAction(timeline)

      if (result.success) {
        setData((prev) => ({
          ...prev,
          timeline: timeline,
        }))
      }
    } catch (err) {
      console.error("Error updating timeline:", err)
      setData((prev) => ({
        ...prev,
        timeline: timeline,
      }))
      throw err
    }
  }

  const updateServiceCards = async (serviceCards: ServiceCard[]) => {
    try {
      const result = await updateServiceCardsAction(serviceCards)

      if (result.success) {
        setData((prev) => ({
          ...prev,
          serviceCards: serviceCards,
        }))
      }
    } catch (err) {
      console.error("Error updating service cards:", err)
      setData((prev) => ({
        ...prev,
        serviceCards: serviceCards,
      }))
      throw err
    }
  }

  const updateStats = async (stats: Stat[]) => {
    try {
      const result = await updateStatsAction(stats)

      if (result.success) {
        setData((prev) => ({
          ...prev,
          stats: stats,
        }))
      }
    } catch (err) {
      console.error("Error updating stats:", err)
      setData((prev) => ({
        ...prev,
        stats: stats,
      }))
      throw err
    }
  }

  return (
    <PortfolioContext.Provider
      value={{
        data,
        isLoading,
        error,
        updatePersonalInfo,
        updateTechnologies,
        addGalleryItem,
        updateGalleryItem,
        deleteGalleryItem,
        updateSocialLinks,
        updateGithubStats,
        updateSkills,
        updateAboutMe,
        updateWorkAreas,
        updateTimeline,
        updateServiceCards,
        updateStats,
        refreshData,
      }}
    >
      {children}
    </PortfolioContext.Provider>
  )
}

export function usePortfolio() {
  const context = useContext(PortfolioContext)
  if (context === undefined) {
    throw new Error("usePortfolio must be used within a PortfolioProvider")
  }
  return context
}

// Export types for use in other components
export type { PersonalInfo, GalleryItem, SocialLink, GithubStats, Skill, TimelineItem, ServiceCard, Stat }