"use client"

import { usePortfolio, type TimelineItem } from "@/contexts/portfolio-context"
import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Briefcase, GraduationCap, Plus, Trash2, GripVertical, Save, AlertCircle } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"

export default function TimelineAdminPage() {
  const { data, updateTimeline } = usePortfolio()
  const [timeline, setTimeline] = useState<TimelineItem[]>(data.timeline || [])
  const [newItem, setNewItem] = useState({
    type: "work",
    title: "",
    org: "",
    time: "",
    description: ""
  })
  const [loading, setLoading] = useState(false)

  // Update local timeline when data changes
  useEffect(() => {
    setTimeline(data.timeline || [])
  }, [data.timeline])

  const handleChange = (idx: number, field: keyof TimelineItem, value: string) => {
    setTimeline(timeline => timeline.map((item, i) =>
      i === idx
        ? { ...item, [field]: field === "type" ? (value as "work" | "education") : value }
        : item
    ))
  }

  const handleSave = async () => {
    setLoading(true)
    try {
      await updateTimeline(timeline)
      toast({
        title: "Timeline updated",
        description: "Your timeline has been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating timeline:", error)
      toast({
        title: "Error",
        description: "Failed to update timeline. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleAdd = () => {
    if (!newItem.title.trim() || !newItem.org.trim()) {
      toast({
        title: "Error",
        description: "Please fill in title and organization fields.",
        variant: "destructive",
      })
      return
    }

    setTimeline([
      ...timeline,
      {
        id: Date.now(),
        type: newItem.type as "work" | "education",
        title: newItem.title,
        org: newItem.org,
        time: newItem.time,
        description: newItem.description
      } satisfies TimelineItem
    ])
    setNewItem({ type: "work", title: "", org: "", time: "", description: "" })
  }

  const handleDelete = (idx: number) => {
    setTimeline(timeline => timeline.filter((_, i) => i !== idx))
  }

  const validateItem = (item: any) => {
    return item.title.trim() && item.org.trim() && item.time.trim()
  }

  const hasUnsavedChanges = JSON.stringify(timeline) !== JSON.stringify(data.timeline || [])

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white">
      <div className="max-w-4xl mx-auto py-6 sm:py-8 px-4 sm:px-6">
        <div className="mb-6 sm:mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl sm:text-3xl font-bold mb-2 flex items-center gap-3">
                <div className="p-2 bg-blue-500/20 rounded-lg">
                  <Briefcase className="text-blue-400 w-5 h-5 sm:w-6 sm:h-6" />
                </div>
                <span className="hidden sm:inline">Timeline Management</span>
                <span className="sm:hidden">Timeline</span>
              </h1>
              <p className="text-gray-400 text-sm sm:text-base">Manage your professional timeline and educational background</p>
            </div>
            {hasUnsavedChanges && (
              <div className="flex items-center gap-2 text-yellow-400 text-sm">
                <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                <span className="hidden sm:inline">Unsaved changes</span>
                <span className="sm:hidden">Unsaved</span>
              </div>
            )}
          </div>
        </div>

        <div className="space-y-6">
          {timeline.map((item, idx) => {
            const isValid = validateItem(item)
            return (
              <div key={item.id} className={`bg-gray-800/50 backdrop-blur-sm border rounded-xl p-4 sm:p-6 relative hover:border-gray-600/50 transition-all duration-200 ${
                isValid ? 'border-gray-700/50' : 'border-red-500/50'
              }`}>
                <div className="absolute left-2 top-1/2 -translate-y-1/2 cursor-grab active:cursor-grabbing">
                  <GripVertical className="w-4 h-4 text-gray-500 hover:text-gray-400" />
                </div>
                
                <div className="absolute top-3 right-3 sm:top-4 sm:right-4 flex gap-2">
                  {!isValid && (
                    <div className="flex items-center text-red-400" title="Missing required fields">
                      <AlertCircle className="w-4 h-4" />
                    </div>
                  )}
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={() => handleDelete(idx)}
                    className="text-red-400 hover:text-red-300 hover:bg-red-500/10 h-8 w-8 sm:h-10 sm:w-10"
                  >
                    <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
                  </Button>
                </div>

                <div className="space-y-3 sm:space-y-4 pl-6 pr-8 sm:pr-12">
                  <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
                    <div className="flex items-center gap-3 w-full sm:w-auto">
                      <div className={`p-2 rounded-lg ${item.type === "education" ? "bg-purple-500/20" : "bg-blue-500/20"}`}>
                        {item.type === "education" ? 
                          <GraduationCap className="text-purple-400 w-4 h-4 sm:w-5 sm:h-5" /> : 
                          <Briefcase className="text-blue-400 w-4 h-4 sm:w-5 sm:h-5" />
                        }
                      </div>
                      <select 
                        className="bg-gray-700 border border-gray-600 rounded-lg px-2 py-1.5 sm:px-3 sm:py-2 text-white text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none" 
                        value={item.type} 
                        onChange={e => handleChange(idx, "type", e.target.value)}
                      >
                        <option value="work">Work</option>
                        <option value="education">Education</option>
                      </select>
                    </div>
                    <Input 
                      value={item.title} 
                      onChange={e => handleChange(idx, "title", e.target.value)} 
                      placeholder="Position/Degree Title" 
                      className="flex-1 bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 text-sm sm:text-base"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input 
                      value={item.org} 
                      onChange={e => handleChange(idx, "org", e.target.value)} 
                      placeholder="Company/Institution" 
                      className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
                    />
                    <Input 
                      value={item.time} 
                      onChange={e => handleChange(idx, "time", e.target.value)} 
                      placeholder="Duration (e.g. 2020 - 2023)" 
                      className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
                    />
                  </div>
                  
                  <Textarea 
                    value={item.description} 
                    onChange={e => handleChange(idx, "description", e.target.value)} 
                    placeholder="Describe your role, achievements, and key responsibilities..." 
                    className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 min-h-[100px]"
                  />
                </div>
              </div>
            )
          })}
        </div>

        <div className="mt-6 sm:mt-8 p-4 sm:p-6 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/20 rounded-xl">
          <h2 className="text-lg sm:text-xl font-semibold mb-3 sm:mb-4 flex items-center gap-3">
            <div className="p-2 bg-green-500/20 rounded-lg">
              <Plus className="w-4 h-4 sm:w-5 sm:h-5 text-green-400" />
            </div>
            <span className="hidden sm:inline">Add New Timeline Item</span>
            <span className="sm:hidden">Add New Item</span>
          </h2>
          
          <div className="space-y-3 sm:space-y-4">
            <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center">
              <select 
                className="bg-gray-700 border border-gray-600 rounded-lg px-2 py-1.5 sm:px-3 sm:py-2 text-white text-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 outline-none w-full sm:w-auto" 
                value={newItem.type} 
                onChange={e => setNewItem({ ...newItem, type: e.target.value })}
              >
                <option value="work">Work Experience</option>
                <option value="education">Education</option>
              </select>
              <Input 
                value={newItem.title} 
                onChange={e => setNewItem({ ...newItem, title: e.target.value })} 
                placeholder="Position/Degree Title" 
                className="flex-1 bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 text-sm sm:text-base"
              />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Input 
                value={newItem.org} 
                onChange={e => setNewItem({ ...newItem, org: e.target.value })} 
                placeholder="Company/Institution" 
                className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
              />
              <Input 
                value={newItem.time} 
                onChange={e => setNewItem({ ...newItem, time: e.target.value })} 
                placeholder="Duration (e.g. 2020 - 2023)" 
                className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500"
              />
            </div>
            
            <Textarea 
              value={newItem.description} 
              onChange={e => setNewItem({ ...newItem, description: e.target.value })} 
              placeholder="Describe your role, achievements, and key responsibilities..." 
              className="bg-gray-700 border-gray-600 text-white placeholder-gray-400 focus:border-blue-500 min-h-[100px]"
            />
            
            <Button 
              onClick={handleAdd} 
              className="bg-green-600 hover:bg-green-700 text-white w-full sm:w-auto"
            >
              <Plus className="w-4 h-4 mr-2" /> 
              <span className="hidden sm:inline">Add Timeline Item</span>
              <span className="sm:hidden">Add Item</span>
            </Button>
          </div>
        </div>

        <div className="mt-6 sm:mt-8 flex gap-4">
          <Button 
            onClick={handleSave} 
            className={`flex-1 py-2.5 sm:py-3 text-base sm:text-lg font-medium transition-all duration-200 ${
              hasUnsavedChanges 
                ? 'bg-blue-600 hover:bg-blue-700 text-white' 
                : 'bg-gray-600 text-gray-300 cursor-not-allowed'
            }`}
            disabled={loading || !hasUnsavedChanges}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                <span className="hidden sm:inline">Saving Changes...</span>
                <span className="sm:hidden">Saving...</span>
              </>
            ) : hasUnsavedChanges ? (
              <>
                <Save className="w-4 h-4 mr-2" />
                <span className="hidden sm:inline">Save All Changes</span>
                <span className="sm:hidden">Save Changes</span>
              </>
            ) : (
              <>
                <span className="hidden sm:inline">All Changes Saved</span>
                <span className="sm:hidden">Saved</span>
              </>
            )}
          </Button>
        </div>

        <div className="mt-4 text-center text-sm text-gray-400">
          {timeline.length} timeline {timeline.length === 1 ? 'item' : 'items'} • 
          {timeline.filter(validateItem).length} valid • 
          {timeline.filter(item => !validateItem(item)).length} incomplete
        </div>

        <Toaster />
      </div>
    </div>
  )
}
