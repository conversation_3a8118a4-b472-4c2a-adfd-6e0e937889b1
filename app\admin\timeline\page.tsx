"use client"

import { usePortfolio, type TimelineItem } from "@/contexts/portfolio-context"
import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Briefcase, GraduationCap, Plus, Trash2 } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"

export default function TimelineAdminPage() {
  const { data, updateTimeline } = usePortfolio()
  const [timeline, setTimeline] = useState<TimelineItem[]>(data.timeline || [])
  const [newItem, setNewItem] = useState({
    type: "work",
    title: "",
    org: "",
    time: "",
    description: ""
  })
  const [loading, setLoading] = useState(false)

  // Update local timeline when data changes
  useEffect(() => {
    setTimeline(data.timeline || [])
  }, [data.timeline])

  const handleChange = (idx: number, field: keyof TimelineItem, value: string) => {
    setTimeline(timeline => timeline.map((item, i) =>
      i === idx
        ? { ...item, [field]: field === "type" ? (value as "work" | "education") : value }
        : item
    ))
  }

  const handleSave = async () => {
    setLoading(true)
    try {
      await updateTimeline(timeline)
      toast({
        title: "Timeline updated",
        description: "Your timeline has been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating timeline:", error)
      toast({
        title: "Error",
        description: "Failed to update timeline. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(false)
    }
  }

  const handleAdd = () => {
    if (!newItem.title.trim() || !newItem.org.trim()) {
      toast({
        title: "Error",
        description: "Please fill in title and organization fields.",
        variant: "destructive",
      })
      return
    }

    setTimeline([
      ...timeline,
      {
        id: Date.now(),
        type: newItem.type as "work" | "education",
        title: newItem.title,
        org: newItem.org,
        time: newItem.time,
        description: newItem.description
      } satisfies TimelineItem
    ])
    setNewItem({ type: "work", title: "", org: "", time: "", description: "" })
  }

  const handleDelete = (idx: number) => {
    setTimeline(timeline => timeline.filter((_, i) => i !== idx))
  }

  return (
    <div className="max-w-2xl mx-auto py-10">
      <h1 className="text-2xl font-bold mb-6 flex items-center gap-2">
        <Briefcase className="text-blue-500" /> Timeline Management
      </h1>
      <div className="space-y-8">
        {timeline.map((item, idx) => (
          <div key={item.id} className="bg-white/80 rounded-xl shadow p-4 flex flex-col gap-2 relative">
            <div className="absolute top-2 right-2">
              <Button variant="ghost" size="icon" onClick={() => handleDelete(idx)}>
                <Trash2 className="w-4 h-4 text-red-500" />
              </Button>
            </div>
            <div className="flex gap-2 items-center">
              {item.type === "education" ? <GraduationCap className="text-purple-500" /> : <Briefcase className="text-blue-500" />}
              <select className="border rounded px-2 py-1" value={item.type} onChange={e => handleChange(idx, "type", e.target.value)}>
                <option value="work">Work</option>
                <option value="education">Education</option>
              </select>
              <Input value={item.title} onChange={e => handleChange(idx, "title", e.target.value)} placeholder="Title" className="flex-1" />
            </div>
            <Input value={item.org} onChange={e => handleChange(idx, "org", e.target.value)} placeholder="Organization" />
            <Input value={item.time} onChange={e => handleChange(idx, "time", e.target.value)} placeholder="Time (e.g. 2020-2023)" />
            <Textarea value={item.description} onChange={e => handleChange(idx, "description", e.target.value)} placeholder="Description" />
          </div>
        ))}
      </div>
      <div className="mt-8 p-4 bg-blue-50 rounded-xl">
        <h2 className="font-semibold mb-2 flex items-center gap-2"><Plus className="w-4 h-4" /> Add New Timeline Item</h2>
        <div className="flex gap-2 items-center mb-2">
          <select className="border rounded px-2 py-1" value={newItem.type} onChange={e => setNewItem({ ...newItem, type: e.target.value })}>
            <option value="work">Work</option>
            <option value="education">Education</option>
          </select>
          <Input value={newItem.title} onChange={e => setNewItem({ ...newItem, title: e.target.value })} placeholder="Title" className="flex-1" />
        </div>
        <Input value={newItem.org} onChange={e => setNewItem({ ...newItem, org: e.target.value })} placeholder="Organization" className="mb-2" />
        <Input value={newItem.time} onChange={e => setNewItem({ ...newItem, time: e.target.value })} placeholder="Time (e.g. 2020-2023)" className="mb-2" />
        <Textarea value={newItem.description} onChange={e => setNewItem({ ...newItem, description: e.target.value })} placeholder="Description" className="mb-2" />
        <Button onClick={handleAdd} variant="secondary" className="mt-2"><Plus className="w-4 h-4 mr-1" /> Add</Button>
      </div>
      <Button onClick={handleSave} className="mt-8 w-full" disabled={loading}>
        {loading ? "Saving..." : "Save All Changes"}
      </Button>
      <Toaster />
    </div>
  )
}