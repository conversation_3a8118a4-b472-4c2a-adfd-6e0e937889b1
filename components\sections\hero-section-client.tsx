"use client"

import Image from "next/image"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Palette } from "lucide-react"
import { R<PERSON>pleButton } from "@/components/animate-ui/buttons/ripple"
import { GradientText } from "@/components/animate-ui/text/gradient"
import { TypingText } from "@/components/animate-ui/text/typing"
import { ShimmeringText } from "@/components/animate-ui/text/shimmering"
import AnimatedBackground from "@/components/animated-background"
import { motion } from "framer-motion"

export type PersonalInfo = {
  name: string
  role: string
  bio: string
  location: string
  website: string
  email: string
  profileImage?: string
}

interface HeroSectionProps {
  personalInfo: PersonalInfo
}

export default function HeroSectionClient({ personalInfo }: HeroSectionProps) {
  const fullText = personalInfo.role || "Full Stack Developer"

  const floatingIcons = [
    { Icon: Code, delay: 0, position: "top-10 left-10" },
    { Icon: Palette, delay: 2, position: "top-20 right-20" },
    { Icon: Sparkles, delay: 4, position: "bottom-20 left-20" }
  ]

  return (
    <motion.section
      id="home"
      initial={{ opacity: 0, y: 40 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.2 }}
      transition={{ duration: 0.7, ease: "easeOut" }}
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gray-950 text-white"
      aria-label="Hero section"
    >
      <AnimatedBackground />
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-900 to-purple-900 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-pink-900 to-red-900 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-float" style={{ animationDelay: '2s' }}></div>
        <div className="absolute top-40 left-1/2 transform -translate-x-1/2 w-80 h-80 bg-gradient-to-br from-yellow-700 to-orange-900 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-float" style={{ animationDelay: '4s' }}></div>
      </div>

      {/* Floating Icons */}
      {floatingIcons.map(({ Icon, position }, index) => (
        <div
          key={index}
          className={`absolute ${position} text-blue-500 opacity-20 animate-float`}
          style={{ animationDelay: `${index * 2}s` }}
        >
          <Icon size={40} />
        </div>
      ))}

      <div className="container mx-auto px-6 md:px-12 lg:px-24 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center animate-fade-in-up">
          {/* Left Content */}
          <div className="space-y-8">
            <div className="animate-fade-in-up">
              <h1 className="text-5xl md:text-7xl font-bold leading-tight text-white">
                Hello, I'm{" "}
                <GradientText text={personalInfo.name} />
              </h1>
            </div>

            <div className="h-16 animate-fade-in-up">
              <p className="text-2xl md:text-3xl text-gray-200 font-light">
                <TypingText text={fullText} cursor />
              </p>
            </div>

            <div className="animate-fade-in-up">
              <p className="text-lg text-gray-300 max-w-lg leading-relaxed">
                {personalInfo.bio}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 animate-fade-in-up">
              <RippleButton
                size="lg"
                variant="default"
                className="group bg-gradient-to-r from-blue-800 to-purple-800 hover:from-blue-900 hover:to-purple-900 text-white shadow-lg hover:shadow-xl transition-all duration-300 animate-glow w-full sm:w-auto"
                onClick={e => {
                  e.preventDefault();
                  const el = document.querySelector('#projects');
                  if (el) el.scrollIntoView({ behavior: 'smooth' });
                }}
                asChild
              >
                <a href="#projects">
                  View My Work
                  <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform" />
                </a>
              </RippleButton>
              <RippleButton
                variant="default"
                size="lg"
                className="group  text-white"
                onClick={e => {
                  e.preventDefault();
                  const el = document.querySelector('#contact');
                  if (el) el.scrollIntoView({ behavior: 'smooth' });
                }}
                asChild
              >
                <a href="#contact">
                  Get in Touch
                  <Sparkles className="ml-2 h-5 w-5 group-hover:rotate-12 transition-transform drop-shadow-glow" />
                </a>
              </RippleButton>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-8 pt-8 border-t border-gray-700 animate-fade-in-up">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-300 animate-bounce-in">
                  <ShimmeringText text="50+" />
                </div>
                <div className="text-sm text-gray-400">Projects</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-300 animate-bounce-in" style={{ animationDelay: '0.2s' }}>
                  <ShimmeringText text="3+" />
                </div>
                <div className="text-sm text-gray-400">Years Experience</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-pink-300 animate-bounce-in" style={{ animationDelay: '0.4s' }}>
                  <ShimmeringText text="100%" />
                </div>
                <div className="text-sm text-gray-400">Client Satisfaction</div>
              </div>
            </div>
          </div>

          {/* Right Content - Profile Image */}
          <div className="relative animate-fade-in-up">
            <motion.div
              className="relative mx-auto w-80 h-80 lg:w-96 lg:h-96"
              initial={{ opacity: 0, scale: 0.8, rotate: -10 }}
              animate={{ opacity: 1, scale: 1, rotate: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              whileHover={{ scale: 1.04, rotate: 2, boxShadow: "0 8px 32px 0 rgba(80,80,255,0.25)" }}
            >
              {/* Animated Rings */}
              <div className="absolute inset-0 rounded-full border-4 border-blue-900 animate-spin" style={{ animationDuration: '20s' }}></div>
              <div className="absolute inset-4 rounded-full border-4 border-purple-900 animate-spin" style={{ animationDuration: '15s', animationDirection: 'reverse' }}></div>
              <div className="absolute inset-8 rounded-full border-4 border-pink-900 animate-spin" style={{ animationDuration: '10s' }}></div>
              {/* Profile Image */}
              <motion.div
                className="absolute inset-12 rounded-full overflow-hidden shadow-2xl animate-float"
                initial={{ scale: 0.95, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.7, ease: "easeOut" }}
                whileHover={{ scale: 1.05 }}
              >
                <Image
                  src={personalInfo.profileImage || "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop&crop=face"}
                  alt={`${personalInfo.name}'s profile`}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 320px, (max-width: 1024px) 384px, 400px"
                  priority
                />
              </motion.div>
              {/* Floating Orbs */}
              <div className="absolute -top-4 -right-4 w-16 h-16 bg-gradient-to-br from-blue-800 to-blue-900 rounded-full shadow-lg animate-float" style={{ animationDelay: '0s' }} />
              <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-gradient-to-br from-purple-800 to-purple-900 rounded-full shadow-lg animate-float" style={{ animationDelay: '1s' }} />
              <div className="absolute top-1/2 -right-8 w-8 h-8 bg-gradient-to-br from-pink-800 to-pink-900 rounded-full shadow-lg animate-float" style={{ animationDelay: '2s' }} />
            </motion.div>
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-gray-500 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse"></div>
        </div>
      </div>
    </motion.section>
  )
}
