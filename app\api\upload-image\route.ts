import { NextRequest, NextResponse } from "next/server"
import { saveImageFile, getFileExtension } from "@/utils/local-image-storage"
import { validateFileUpload, checkRateLimit, sanitizeFileName } from "@/lib/validation"

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = request.ip || request.headers.get('x-forwarded-for') || 'unknown'
    if (!checkRateLimit(clientIP, 10, 60000)) { // 10 uploads per minute
      return NextResponse.json(
        { success: false, error: "Too many requests. Please try again later." },
        { status: 429 }
      )
    }

    const formData = await request.formData()
    const file = formData.get("file") as File
    const type = formData.get("type") as string || "general"

    // Validate file upload
    const validation = validateFileUpload(file, type)
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.errors.join(", ") },
        { status: 400 }
      )
    }

    // Generate filename based on type
    const extension = getFileExtension(file.type)
    let filename: string
    
    switch (type) {
      case "avatar":
        filename = `avatar.${extension}`
        break
      case "project":
        const projectId = formData.get("projectId") as string
        filename = projectId ? `project-${projectId}.${extension}` : `project-${Date.now()}.${extension}`
        break
      default:
        filename = `${type}-${Date.now()}.${extension}`
    }

    // Save the file
    const result = await saveImageFile(file, filename)
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        url: result.url,
        filename
      })
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("Error uploading image:", error)
    return NextResponse.json(
      { success: false, error: "Failed to upload image" },
      { status: 500 }
    )
  }
}