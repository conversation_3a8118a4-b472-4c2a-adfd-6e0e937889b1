import { NextRequest, NextResponse } from "next/server"
import { saveImageFile, getFileExtension } from "@/utils/local-image-storage"

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get("file") as File
    const type = formData.get("type") as string || "general"
    
    if (!file) {
      return NextResponse.json(
        { success: false, error: "No file provided" },
        { status: 400 }
      )
    }

    // Validate file type
    if (!file.type.startsWith("image/")) {
      return NextResponse.json(
        { success: false, error: "File must be an image" },
        { status: 400 }
      )
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { success: false, error: "File size must be less than 5MB" },
        { status: 400 }
      )
    }

    // Generate filename based on type
    const extension = getFileExtension(file.type)
    let filename: string
    
    switch (type) {
      case "avatar":
        filename = `avatar.${extension}`
        break
      case "project":
        const projectId = formData.get("projectId") as string
        filename = projectId ? `project-${projectId}.${extension}` : `project-${Date.now()}.${extension}`
        break
      default:
        filename = `${type}-${Date.now()}.${extension}`
    }

    // Save the file
    const result = await saveImageFile(file, filename)
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        url: result.url,
        filename
      })
    } else {
      return NextResponse.json(
        { success: false, error: result.error },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("Error uploading image:", error)
    return NextResponse.json(
      { success: false, error: "Failed to upload image" },
      { status: 500 }
    )
  }
}