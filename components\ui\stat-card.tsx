interface StatCardProps {
  title: string
  value: number | string
  description: string
  className?: string
  iconColor?: string
}

export default function StatCard(props: StatCardProps) {
  const { title, value, description, className = "", iconColor = "" } = props
  return (
    <div className={`p-6 rounded-2xl border shadow-lg transition-all duration-300 ${className}`}>
      <h3 className="text-lg font-semibold mb-2 tracking-wide uppercase opacity-80">{title}</h3>
      <p className={`text-4xl font-extrabold mb-1 ${iconColor}`}>{value}</p>
      <p className="text-sm opacity-70">{description}</p>
    </div>
  )
}
