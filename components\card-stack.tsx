"use client"

import { useState } from "react"
import Image from "next/image"
import { motion, useMotionValue, useTransform, PanInfo } from "framer-motion"
import { Sparkles, Code, Smartphone, Palette, BarChart3, Cloud } from "lucide-react"
import { usePortfolio, type ServiceCard } from "@/contexts/portfolio-context"

const cardIcons = [
  <Code key="code" className="w-8 h-8 text-blue-500 drop-shadow" />,
  <Smartphone key="mobile" className="w-8 h-8 text-green-500 drop-shadow" />,
  <Palette key="palette" className="w-8 h-8 text-purple-500 drop-shadow" />,
  <BarChart3 key="analytics" className="w-8 h-8 text-orange-500 drop-shadow" />,
  <Cloud key="cloud" className="w-8 h-8 text-cyan-500 drop-shadow" />
]

// Default cards as fallback
const defaultCards: ServiceCard[] = [
  {
    id: 1,
    image: "https://images.unsplash.com/photo-1517180102446-f3ece451e9d8?w=400&h=500&fit=crop",
    title: "Web Development",
    description: "Creating modern and responsive websites",
    category: "Frontend"
  },
  {
    id: 2,
    image: "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=500&fit=crop",
    title: "Mobile Apps",
    description: "Building cross-platform mobile applications",
    category: "Mobile"
  },
  {
    id: 3,
    image: "https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=500&fit=crop",
    title: "UI/UX Design",
    description: "Designing beautiful user interfaces",
    category: "Design"
  },
  {
    id: 4,
    image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=500&fit=crop",
    title: "Data Analytics",
    description: "Analyzing data for business insights",
    category: "Analytics"
  },
  {
    id: 5,
    image: "https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=400&h=500&fit=crop",
    title: "Cloud Solutions",
    description: "Deploying scalable cloud infrastructure",
    category: "DevOps"
  }
]

export default function CardStack() {
  const { data } = usePortfolio()
  const [currentIndex, setCurrentIndex] = useState(0)
  const [direction, setDirection] = useState<"left" | "right" | null>(null)

  // Use service cards from portfolio data, fallback to default cards
  const cards = data.serviceCards && data.serviceCards.length > 0 ? data.serviceCards : defaultCards

  // Create ordered cards array based on current index
  const getOrderedCards = () => {
    const orderedCards = []
    for (let i = 0; i < cards.length; i++) {
      const cardIndex = (currentIndex + i) % cards.length
      orderedCards.push(cards[cardIndex])
    }
    return orderedCards
  }

  const handleSwipe = (swipeDirection: "left" | "right") => {
    setDirection(swipeDirection)
    
    // Move to next card (infinite loop)
    if (swipeDirection === "right") {
      setCurrentIndex((prev) => (prev + 1) % cards.length)
    } else {
      setCurrentIndex((prev) => (prev - 1 + cards.length) % cards.length)
    }
  }

  const orderedCards = getOrderedCards()

  return (
    <div className="relative w-80 h-96 mx-auto">
      {/* Card Stack */}
      <div className="relative w-full h-full">
        {orderedCards.map((card, index) => (
          <SwipeCard
            key={`${card.id}-${currentIndex}`}
            card={card}
            index={index}
            total={orderedCards.length}
            onSwipe={handleSwipe}
            isTopCard={index === 0}
          />
        ))}
      </div>
    </div>
  )
}

interface SwipeCardProps {
  card: ServiceCard
  index: number
  total: number
  onSwipe: (direction: "left" | "right") => void
  isTopCard: boolean
}

function SwipeCard({ card, index, total, onSwipe, isTopCard }: SwipeCardProps) {
  const x = useMotionValue(0)
  const rotate = useTransform(x, [-200, 200], [-25, 25])
  const opacity = useTransform(x, [-200, -100, 0, 100, 200], [0, 1, 1, 1, 0])

  const [isDragging, setIsDragging] = useState(false)

  const handleDragEnd = (event: any, info: PanInfo) => {
    setIsDragging(false)
    
    // Only allow swipe on the top card
    if (!isTopCard) return
    
    const swipeThreshold = 100
    if (Math.abs(info.offset.x) > swipeThreshold) {
      const direction = info.offset.x > 0 ? "right" : "left"
      onSwipe(direction)
    }
  }

  const handleDragStart = () => {
    if (isTopCard) {
      setIsDragging(true)
    }
  }

  // Calculate staggered positioning - zic zac effect
  const getCardTransform = (index: number) => {
    const baseScale = 1 - (index * 0.04)
    const baseY = index * -10
    const baseRotation = index % 2 === 0 ? index * 2 : -(index * 2) // Alternating rotation for zic zac
    const baseX = index % 2 === 0 ? index * 3 : -(index * 3) // Alternating X offset
    
    return {
      scale: baseScale,
      y: baseY,
      rotateZ: baseRotation,
      x: baseX,
    }
  }

  const cardTransform = getCardTransform(index)
  const zIndex = total - index

  return (
    <motion.div
      className={`absolute inset-0 ${isTopCard ? 'cursor-grab active:cursor-grabbing' : 'cursor-default'} group`}
      style={{
        x: isTopCard ? x : 0,
        rotate: isTopCard ? rotate : cardTransform.rotateZ,
        opacity: isTopCard ? opacity : 1,
        zIndex,
        filter: isTopCard ? 'drop-shadow(0 8px 32px rgba(80,80,255,0.15))' : 'none',
      }}
      initial={{
        ...cardTransform,
        opacity: 0,
        scale: 0.8,
      }}
      animate={{
        ...cardTransform,
        opacity: 1,
        scale: isDragging && isTopCard ? 1.07 : cardTransform.scale,
        y: isDragging && isTopCard ? -8 : cardTransform.y,
        rotateZ: isDragging && isTopCard ? 0 : cardTransform.rotateZ,
        x: isDragging && isTopCard ? 0 : cardTransform.x,
      }}
      drag={isTopCard ? "x" : false}
      dragConstraints={{ left: 0, right: 0 }}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      whileTap={isTopCard ? { scale: 1.04 } : {}}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 30
      }}
    >
      <div className={`w-full h-full bg-white/70 backdrop-blur-2xl rounded-3xl shadow-[0_8px_40px_0_rgba(80,80,255,0.18)] overflow-hidden relative ${
        isTopCard ? 'shadow-2xl' : 'shadow-lg'
      }`}
        style={{
          background: isTopCard ? 'linear-gradient(135deg, #e0e7ff 0%, #f3e8ff 100%)' : 'linear-gradient(135deg, #f3f4f6 0%, #e0e7ff 100%)',
          boxShadow: isTopCard
            ? '0 8px 40px 0 rgba(80,80,255,0.18), 0 0 0 8px rgba(124,58,237,0.08)'
            : '0 2px 12px 0 rgba(80,80,255,0.10)',
        }}
      >
        {/* Card Image + Icon */}
        <div className="relative h-2/3 overflow-hidden flex items-center justify-center">
          <Image
            src={card.image}
            alt={card.title}
            fill
            className={`object-cover transition-transform duration-700 scale-100 group-hover:scale-110 ${isTopCard ? '' : 'opacity-80 grayscale'}`}
            draggable={false}
            style={{
              borderRadius: '1.5rem',
              boxShadow: isTopCard ? '0 4px 32px 0 rgba(124,58,237,0.10)' : undefined,
            }}
            sizes="(max-width: 768px) 280px, 320px"
          />
          {/* Creative Icon */}
          <div className="absolute top-4 right-4 z-10">
            {cardIcons[index % cardIcons.length]}
          </div>
          {/* Gradient Overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent pointer-events-none rounded-3xl" />
          {/* Category Badge */}
          <div className="absolute top-4 left-4">
            <span className="bg-white/80 backdrop-blur text-gray-800 px-3 py-1 rounded-full text-xs font-semibold shadow-lg">
              {card.category}
            </span>
          </div>
          {/* Swipe Indicators - Only show on top card */}
          {isTopCard && (
            <>
              <motion.div
                className="absolute top-1/2 left-8 transform -translate-y-1/2 bg-blue-500/90 text-white px-4 py-2 rounded-full font-bold text-lg shadow-lg backdrop-blur-sm animate-pulse"
                style={{
                  opacity: useTransform(x, [-100, -50, 0], [1, 0.5, 0]),
                  scale: useTransform(x, [-100, -50, 0], [1, 0.8, 0.5]),
                }}
              >
                PREV
              </motion.div>
              <motion.div
                className="absolute top-1/2 right-8 transform -translate-y-1/2 bg-green-500/90 text-white px-4 py-2 rounded-full font-bold text-lg shadow-lg backdrop-blur-sm animate-pulse"
                style={{
                  opacity: useTransform(x, [0, 50, 100], [0, 0.5, 1]),
                  scale: useTransform(x, [0, 50, 100], [0.5, 0.8, 1]),
                }}
              >
                NEXT
              </motion.div>
            </>
          )}
          {/* Card depth indicator */}
          <div className="absolute top-4 right-16">
            <div className={`backdrop-blur-sm text-white w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
              isTopCard ? 'bg-blue-500/80' : 'bg-black/50'
            }`}>
              {index + 1}
            </div>
          </div>
        </div>
        {/* Card Content */}
        <div className="p-6 h-1/3 flex flex-col justify-center relative">
          <h3 className={`text-2xl font-extrabold mb-2 transition-colors bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent ${
            isTopCard ? '' : 'opacity-60'
          }`}>
            {card.title}
          </h3>
          <p className={`text-base leading-relaxed ${
            isTopCard ? 'text-gray-700' : 'text-gray-400'
          }`}>
            {card.description}
          </p>
          {/* Decorative sparkles */}
          {isTopCard && (
            <Sparkles className="absolute bottom-4 left-4 text-yellow-400 opacity-60 animate-float" />
          )}
          {/* Decorative elements */}
          <div className="absolute bottom-2 right-4 opacity-10">
            <div className="w-16 h-16 border-2 border-gray-300/40 rounded-full"></div>
            <div className="absolute top-2 left-2 w-12 h-12 border-2 border-gray-400/30 rounded-full"></div>
          </div>
        </div>
        {/* Drag Handle Indicator - Only show on top card */}
        {isTopCard && (
          <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
              <div className="w-6 h-2 bg-blue-400 rounded-full"></div>
              <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
            </div>
          </div>
        )}
        {/* Top card glow effect */}
        {isTopCard && (
          <div className="absolute inset-0 rounded-3xl ring-4 ring-blue-400/30 ring-offset-2 ring-offset-white pointer-events-none animate-pulse"></div>
        )}
      </div>
    </motion.div>
  )
}