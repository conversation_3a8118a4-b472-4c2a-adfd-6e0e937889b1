{"personalInfo": {"name": "truongnat", "role": "Software Engineer", "bio": "Tell us about yourself...", "location": "Gia Trung - Gia Vien - <PERSON>", "website": "https://yourwebsite.com", "email": "<EMAIL>", "profileImage": "/images/avatar.jpg"}, "aboutMe": "I'm a passionate developer with a strong background in web and mobile development. I love building products that make a difference.", "workAreas": ["Web Development", "Mobile Development", "UI/UX Design", "Branding"], "technologies": ["JavaScript", "TypeScript", "React", "Next.js", "Node.js"], "timeline": [{"id": 1, "type": "work", "title": "Frontend Developer", "org": "Creative Studio", "time": "2023 - Present", "description": "Design and develop modern web interfaces, optimizing user experience for many startup projects."}, {"id": 2, "type": "work", "title": "UI/UX Designer", "org": "Freelance", "time": "2021 - 2023", "description": "Consulting and designing UI/UX for digital products, mobile applications and websites."}, {"id": 3, "type": "education", "title": "Bachelor of Information Technology", "org": "UIT - VNUHCM", "time": "2017 - 2021", "description": "Graduated with honors, majoring in Information Technology."}], "gallery": [{"id": "gallery-1", "title": "Modern Web Design", "description": "A sleek and modern website design with clean aesthetics", "imageUrl": "https://images.unsplash.com/photo-1467232004584-a241de8bcf5d?w=500&h=600&fit=crop", "category": "Web Design", "tags": ["UI/UX", "Modern", "Clean"]}, {"id": "gallery-2", "title": "Mobile App Interface", "description": "Intuitive mobile application interface design", "imageUrl": "https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?w=500&h=400&fit=crop", "category": "Mobile", "tags": ["Mobile", "App", "Interface"]}, {"id": "gallery-3", "title": "Brand Identity", "description": "Complete brand identity design with logo and guidelines", "imageUrl": "https://images.unsplash.com/photo-1558655146-d09347e92766?w=500&h=700&fit=crop", "category": "Branding", "tags": ["Branding", "Logo", "Identity"]}, {"id": "gallery-4", "title": "E-commerce Platform", "description": "Full-featured e-commerce website with modern design", "imageUrl": "https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=500&h=500&fit=crop", "category": "Web Design", "tags": ["E-commerce", "Shopping", "Modern"]}, {"id": "gallery-5", "title": "Dashboard Design", "description": "Analytics dashboard with data visualization", "imageUrl": "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=500&h=600&fit=crop", "category": "Dashboard", "tags": ["Analytics", "Data", "Dashboard"]}, {"id": "gallery-6", "title": "Creative Portfolio", "description": "Creative portfolio website for artists and designers", "imageUrl": "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=500&h=400&fit=crop", "category": "Portfolio", "tags": ["Creative", "Portfolio", "Art"]}], "socialLinks": [{"platform": "GitHub", "url": "https://github.com/truongnat", "id": "social-1750905008871-<PERSON><PERSON><PERSON><PERSON>"}, {"platform": "LinkedIn", "url": "https://linkedin.com/in/truongnat", "id": "social-1750905008871-ij6sbxy63"}], "githubStats": {"stars": 32, "repos": 22, "followers": 14}, "skills": [{"name": "Frontend Development", "percentage": 95, "color": "bg-blue-500"}, {"name": "Backend Development", "percentage": 88, "color": "bg-green-500"}, {"name": "UI/UX Design", "percentage": 82, "color": "bg-purple-500"}, {"name": "Mobile Development", "percentage": 75, "color": "bg-orange-500"}], "serviceCards": [{"id": 1, "image": "https://images.unsplash.com/photo-1517180102446-f3ece451e9d8?w=400&h=500&fit=crop", "title": "Web Development", "description": "Creating modern and responsive websites", "category": "Frontend"}, {"id": 2, "image": "https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=400&h=500&fit=crop", "title": "Mobile Apps", "description": "Building cross-platform mobile applications", "category": "Mobile"}, {"id": 3, "image": "https://images.unsplash.com/photo-1558655146-d09347e92766?w=400&h=500&fit=crop", "title": "UI/UX Design", "description": "Designing beautiful user interfaces", "category": "Design"}, {"id": 4, "image": "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=500&fit=crop", "title": "Data Analytics", "description": "Analyzing data for business insights", "category": "Analytics"}, {"id": 5, "image": "https://images.unsplash.com/photo-1504639725590-34d0984388bd?w=400&h=500&fit=crop", "title": "Cloud Solutions", "description": "Deploying scalable cloud infrastructure", "category": "DevOps"}], "stats": [{"id": 1, "icon": "Award", "label": "Years Experience", "value": "3+", "color": "text-blue-600"}, {"id": 2, "icon": "Coffee", "label": "Cups of Coffee", "value": "1000+", "color": "text-amber-600"}, {"id": 3, "icon": "Heart", "label": "Happy Clients", "value": "50+", "color": "text-red-600"}, {"id": 4, "icon": "Zap", "label": "Projects Done", "value": "100+", "color": "text-green-600"}]}