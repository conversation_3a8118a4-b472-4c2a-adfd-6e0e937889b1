"use client"
import {  <PERSON>, <PERSON>, Heart, Zap, Target, TrendingUp, Users } from "lucide-react"
import { useIntersectionObserver } from "@/hooks/use-intersection-observer"
import TechStack from "@/components/tech-stack"
import CardStack from "@/components/card-stack"
import { useState, useEffect } from "react"
import { GradientText } from "@/components/animate-ui/text/gradient"
import { ShimmeringText } from "@/components/animate-ui/text/shimmering"
import { motion } from "framer-motion"

export type PersonalInfo = {
  name: string
  role: string
  bio: string
  location: string
  website: string
  email: string
  profileImage?: string
}

interface AboutSectionProps {
  personalInfo: PersonalInfo
  technologies: string[]
  skills: { name: string; percentage: number; color?: string }[]
}

const iconMap = {
  Award,
  Coffee,
  Heart,
  Zap,
  TrendingUp,
  Users,
}

const defaultStats = [
  { id: 1, icon: "Award", label: "Years Experience", value: "3+", color: "text-blue-600" },
  { id: 2, icon: "Coffee", label: "Cups of Coffee", value: "1000+", color: "text-amber-600" },
  { id: 3, icon: "Heart", label: "Happy Clients", value: "50+", color: "text-red-600" },
  { id: 4, icon: "Zap", label: "Projects Done", value: "100+", color: "text-green-600" }
]

const quotes = [
  { text: "Code is like humor. When you have to explain it, it's bad.", author: "<PERSON> House" },
  { text: "First, solve the problem. Then, write the code.", author: "John Johnson" },
  { text: "Experience is the name everyone gives to their mistakes.", author: "Oscar Wilde" },
  { text: "In order to be irreplaceable, one must always be different.", author: "Coco Chanel" },
  { text: "Java is to JavaScript what car is to Carpet.", author: "Chris Heilmann" },
  { text: "Knowledge is power.", author: "Francis Bacon" },
  { text: "Sometimes it pays to stay in bed on Monday, rather than spending the rest of the week debugging Monday's code.", author: "Dan Salomon" },
  { text: "Perfection is achieved not when there is nothing more to add, but rather when there is nothing more to take away.", author: "Antoine de Saint-Exupery" },
  { text: "Ruby is rubbish! PHP is phpantastic!", author: "Nikita Popov" },
  { text: "Code never lies, comments sometimes do.", author: "Ron Jeffries" },
  { text: "Simplicity is the ultimate sophistication.", author: "Leonardo da Vinci" },
  { text: "Make it work, make it right, make it fast.", author: "Kent Beck" },
  { text: "Clean code always looks like it was written by someone who cares.", author: "Robert C. Martin" },
  { text: "The best error message is the one that never shows up.", author: "Thomas Fuchs" },
  { text: "Programming isn't about what you know; it's about what you can figure out.", author: "Chris Pine" }
]

export default function AboutSectionClient({ personalInfo, technologies, skills }: AboutSectionProps) {
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 })
  const stats = defaultStats
  const [currentQuote, setCurrentQuote] = useState(quotes[0])
  const [currentQuoteIndex, setCurrentQuoteIndex] = useState(0)
  const [displayedText, setDisplayedText] = useState("")
  const [displayedAuthor, setDisplayedAuthor] = useState("")
  const [isTyping, setIsTyping] = useState(false)

  useEffect(() => {
    const today = new Date()
    const daySeed = today.getFullYear() * 10000 + (today.getMonth() + 1) * 100 + today.getDate()
    function seededRandom(seed: number) {
      let x = Math.sin(seed) * 10000
      return x - Math.floor(x)
    }
    const index = Math.floor(seededRandom(daySeed) * quotes.length)
    setCurrentQuoteIndex(index)
    setCurrentQuote(quotes[index])
  }, [])

  useEffect(() => {
    if (!currentQuote.text) return
    setDisplayedText("")
    setDisplayedAuthor("")
    setIsTyping(true)
    let textIndex = 0
    let authorIndex = 0
    const typeText = () => {
      if (textIndex < currentQuote.text.length) {
        setDisplayedText(currentQuote.text.slice(0, textIndex + 1))
        textIndex++
        setTimeout(typeText, 50)
      } else {
        const typeAuthor = () => {
          if (authorIndex < currentQuote.author.length) {
            setDisplayedAuthor(currentQuote.author.slice(0, authorIndex + 1))
            authorIndex++
            setTimeout(typeAuthor, 30)
          } else {
            setIsTyping(false)
          }
        }
        setTimeout(typeAuthor, 300)
      }
    }
    const startDelay = setTimeout(typeText, 500)
    return () => clearTimeout(startDelay)
  }, [currentQuote])

  const getIconComponent = (iconName: string) => {
    return iconMap[iconName as keyof typeof iconMap] || Award
  }

  return (
    <motion.section
      initial={{ opacity: 0, y: 40 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.2 }}
      transition={{ duration: 0.7, ease: "easeOut" }}
      className="relative py-24 bg-gray-950 text-white"
      id="about"
      ref={ref}
    >
      <div className="container mx-auto px-4">
        <div className={`text-center mb-16 ${isIntersecting ? 'fade-in-up animate' : 'fade-in-up'}`}> 
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <GradientText text="About Me" />
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Learn more about my journey, skills, and what drives my passion for technology.
          </p>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left Content */}
          <div className={`space-y-8 ${isIntersecting ? 'fade-in-left animate' : 'fade-in-left'}`}>
            {/* Stats Grid */}
            <div className="grid grid-cols-2 gap-6">
              {stats.map((stat, index) => {
                const IconComponent = getIconComponent(stat.icon)
                return (
                  <div
                    key={stat.id}
                    className={`text-center p-4 bg-gray-800 rounded-xl hover:bg-gray-700 transition-colors duration-300 animate-fade-in-up`}
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <IconComponent className={`w-8 h-8 mx-auto mb-2 ${stat.color}`} />
                    <div className="text-2xl font-bold text-gray-100">
                      <ShimmeringText text={stat.value} shimmeringColor="#a855f7" color="#e0e7ff" />
                    </div>
                    <div className="text-sm text-gray-400">{stat.label}</div>
                  </div>
                )
              })}
            </div>
            {/* Skills */}
            <div className="space-y-4">
              <h3 className="text-2xl font-bold text-gray-100 flex items-center">
                <Target className="mr-2 h-6 w-6 text-blue-400" />
                Skills & Expertise
              </h3>
              {skills.map((skill, index) => (
                <div key={skill.name} className="space-y-2">
                  <div className="flex justify-between">
                    <span className="font-medium text-gray-200">{skill.name}</span>
                    <span className="text-gray-400">{skill.percentage}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${skill.color || "bg-blue-500"} animate-grow-bar`}
                      style={{ width: isIntersecting ? `${skill.percentage}%` : 0, transition: 'width 1s cubic-bezier(.4,0,.2,1)' }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>
          {/* Right Content */}
          <div className={`space-y-8 ${isIntersecting ? 'fade-in-right animate' : 'fade-in-right'}`}>
            {/* Card Stack - Interactive Swipeable Cards */}
            <div className="mb-12">
              <CardStack />
            </div>
            {/* Tech Stack */}
            <div className="space-y-6">
              <h3 className="text-2xl font-bold text-gray-100 text-center">
                Technologies I Love
              </h3>
              <TechStack technologies={technologies} />
            </div>
            {/* Quote */}
            <div className="bg-gradient-to-r from-gray-800 to-purple-900 p-6 rounded-2xl border-l-4 border-blue-700 relative">
              <div className="absolute top-2 right-2 text-xs text-gray-500">
                Quote #{currentQuoteIndex + 1}
              </div>
              <blockquote className="text-lg italic text-gray-200 mb-2 min-h-[3rem] flex items-center">
                <span>
                  "{displayedText}
                  {isTyping && displayedAuthor === "" && (
                    <span className="animate-pulse text-blue-400">|</span>
                  )}
                  "
                </span>
              </blockquote>
              <cite className="text-sm text-gray-400 min-h-[1.5rem] flex items-center">
                {displayedAuthor && (
                  <span>
                    - {displayedAuthor}
                    {isTyping && (
                      <span className="animate-pulse text-blue-400 ml-1">|</span>
                    )}
                  </span>
                )}
              </cite>
            </div>
          </div>
        </div>
      </div>
    </motion.section>
  )
}
