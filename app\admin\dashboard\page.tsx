"use client"

import Link from "next/link"
import { usePortfolio } from "@/contexts/portfolio-context"
import { useAuth } from "@/contexts/auth-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { User, Code, Github, Share2, Layers, ArrowRight, RefreshCcw, Shield } from "lucide-react"

export default function DashboardPage() {
  const { data, refreshData } = usePortfolio()
  const { user } = useAuth()

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
          <p className="text-gray-400 mt-2">Manage your portfolio content</p>
        </div>
        <Button onClick={() => refreshData()} variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white">
          <RefreshCcw className="mr-2 h-4 w-4" /> Refresh Data
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm hover:bg-gray-900/70 transition-all duration-200">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-white text-lg">
              <div className="p-2 bg-blue-500/20 rounded-lg mr-3">
                <Shield className="h-5 w-5 text-blue-400" />
              </div>
              Authentication
            </CardTitle>
            <CardDescription className="text-gray-400 ml-12">Your authentication status</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
                <span className="text-sm font-medium text-gray-300">Email</span>
                <span className="text-sm text-white font-mono">{user?.email}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
                <span className="text-sm font-medium text-gray-300">User ID</span>
                <span className="text-xs font-mono text-gray-400 bg-gray-700 px-2 py-1 rounded">{user?.id?.slice(0, 8)}...</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
                <span className="text-sm font-medium text-gray-300">Status</span>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-sm text-green-400 font-medium">Authenticated</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm hover:bg-gray-900/70 transition-all duration-200">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-white text-lg">
              <div className="p-2 bg-purple-500/20 rounded-lg mr-3">
                <User className="h-5 w-5 text-purple-400" />
              </div>
              Personal Info
            </CardTitle>
            <CardDescription className="text-gray-400 ml-12">Update your personal information</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
                <span className="text-sm font-medium text-gray-300">Name</span>
                <span className="text-sm text-white font-medium">{data.personalInfo.name}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
                <span className="text-sm font-medium text-gray-300">Location</span>
                <span className="text-sm text-gray-300">{data.personalInfo.location}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
                <span className="text-sm font-medium text-gray-300">Website</span>
                <span className="text-sm text-blue-400 hover:text-blue-300 transition-colors">{data.personalInfo.website}</span>
              </div>
            </div>
            <Button asChild className="w-full mt-4 bg-purple-600 hover:bg-purple-700 text-white border-0" size="sm">
              <Link href="/admin/personal-info">
                <span className="flex items-center justify-center gap-2">
                  Edit Profile <ArrowRight className="h-4 w-4" />
                </span>
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm hover:bg-gray-900/70 transition-all duration-200">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-white text-lg">
              <div className="p-2 bg-green-500/20 rounded-lg mr-3">
                <Layers className="h-5 w-5 text-green-400" />
              </div>
              Technologies
            </CardTitle>
            <CardDescription className="text-gray-400 ml-12">Manage your tech stack</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
              <span className="text-sm font-medium text-gray-300">Total Technologies</span>
              <span className="text-lg font-bold text-green-400">{data.technologies.length}</span>
            </div>
            <div className="space-y-2">
              <span className="text-sm font-medium text-gray-300">Recent Technologies</span>
              <div className="flex flex-wrap gap-2">
                {data.technologies.slice(0, 6).map((tech) => (
                  <span key={tech} className="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-xs font-medium border border-green-500/30">
                    {tech}
                  </span>
                ))}
                {data.technologies.length > 6 && (
                  <span className="px-3 py-1 bg-gray-700 text-gray-300 rounded-full text-xs font-medium">
                    +{data.technologies.length - 6} more
                  </span>
                )}
              </div>
            </div>
            <Button asChild className="w-full mt-4 bg-green-600 hover:bg-green-700 text-white border-0" size="sm">
              <Link href="/admin/technologies">
                <span className="flex items-center justify-center gap-2">
                  Manage Tech Stack <ArrowRight className="h-4 w-4" />
                </span>
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm hover:bg-gray-900/70 transition-all duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-white text-lg">
              <div className="p-2 bg-orange-500/20 rounded-lg mr-3"><Code className="h-5 w-5 text-orange-400" /></div>Gallery
            </CardTitle>
            <CardDescription className="text-gray-400 ml-12">Manage your portfolio gallery</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3"><div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
                <span className="text-sm font-medium text-gray-300">Total</span>
                <span className="text-sm text-white">{data.gallery.length} gallery items</span>
              </div>
              <ul className="mt-2 space-y-1">
                {data.gallery.slice(0, 3).map((item) => (
                  <li key={item.id} className="text-sm truncate">
                    • {item.title}
                  </li>
                ))}
                {data.gallery.length > 3 && (
                  <li className="text-sm text-gray-400">+{data.gallery.length - 3} more items</li>
                )}
              </ul>
            </div>
            <Button asChild className="w-full mt-4" className="w-full mt-4 bg-orange-600 hover:bg-orange-700 text-white border-0" size="sm">
              <Link href="/admin/gallery">
                Edit <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm hover:bg-gray-900/70 transition-all duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-white text-lg">
              <div className="p-2 bg-pink-500/20 rounded-lg mr-3"><Share2 className="h-5 w-5 text-pink-400" /></div>Social Links
            </CardTitle>
            <CardDescription className="text-gray-400 ml-12">Manage your social media links</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3"><div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
                <span className="text-sm font-medium text-gray-300">Total</span>
                <span className="text-sm text-white">{data.socialLinks.length} links</span>
              </div>
              <ul className="mt-2 space-y-1">
                {data.socialLinks.map((link) => (
                  <li key={link.platform} className="text-sm truncate">
                    • {link.platform}
                  </li>
                ))}
              </ul>
            </div>
            <Button asChild className="w-full mt-4" className="w-full mt-4 bg-pink-600 hover:bg-pink-700 text-white border-0" size="sm">
              <Link href="/admin/social-links">
                Edit <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm hover:bg-gray-900/70 transition-all duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-white text-lg">
              <div className="p-2 bg-gray-500/20 rounded-lg mr-3"><Github className="h-5 w-5 text-gray-400" /></div>GitHub Stats
            </CardTitle>
            <CardDescription className="text-gray-400 ml-12">Update your GitHub statistics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3"><div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
                <span className="text-sm font-medium text-gray-300">Stars</span>
                <span className="text-sm text-white">{data.githubStats.stars}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-300">Repositories</span>
                <span className="text-sm text-white">{data.githubStats.repos}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-300">Followers</span>
                <span className="text-sm text-white">{data.githubStats.followers}</span>
              </div>
            </div>
            <Button asChild className="w-full mt-4" className="w-full mt-4 bg-gray-600 hover:bg-gray-700 text-white border-0" size="sm">
              <Link href="/admin/github-stats">
                Edit <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-blue-900/50 to-purple-900/50 border-blue-700/50 backdrop-blur-sm hover:from-blue-900/70 hover:to-purple-900/70 transition-all duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-lg">Preview Site</CardTitle>
            <CardDescription className="text-gray-400">View your portfolio website</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-400 mb-4">See how your portfolio looks with the current content</p>
            <Button asChild className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0">
              <Link href="/" target="_blank">
                View Portfolio
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}