"use client";

import Link from "next/link";
import { usePortfolio } from "@/contexts/portfolio-context";
import { useAuth } from "@/contexts/auth-context";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  User,
  Code,
  Github,
  Share2,
  Layers,
  RefreshCcw,
  Shield,
} from "lucide-react";
import Authentication from "@/components/admin/dashboard/authentication";
import PersonalInfo from "@/components/admin/dashboard/personal-info";
import Technologies from "@/components/admin/dashboard/technologies";
import Gallery from "@/components/admin/dashboard/gallery";
import SocialLinks from "@/components/admin/dashboard/socials-link";
import GitHubStats from "@/components/admin/dashboard/github-stats";

export default function DashboardPage() {
  const { data, refreshData } = usePortfolio();
  const { user } = useAuth();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
          <p className="text-gray-400 mt-2">Manage your portfolio content</p>
        </div>
        <Button
          onClick={() => refreshData()}
          size="sm"
          className="border-gray-700"
        >
          <RefreshCcw className="mr-2 h-4 w-4" /> Refresh Data
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm hover:bg-gray-900/70 transition-all duration-200">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-white text-lg">
              <div className="p-2 bg-blue-500/20 rounded-lg mr-3">
                <Shield className="h-5 w-5 text-blue-400" />
              </div>
              Authentication
            </CardTitle>
            <CardDescription className="text-gray-400 ml-12">
              Your authentication status
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Authentication user={user} />
          </CardContent>
        </Card>

        <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm hover:bg-gray-900/70 transition-all duration-200">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-white text-lg">
              <div className="p-2 bg-purple-500/20 rounded-lg mr-3">
                <User className="h-5 w-5 text-purple-400" />
              </div>
              Personal Info
            </CardTitle>
            <CardDescription className="text-gray-400 ml-12">
              Update your personal information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <PersonalInfo personalInfo={data.personalInfo} />
          </CardContent>
        </Card>

        <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm hover:bg-gray-900/70 transition-all duration-200">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center text-white text-lg">
              <div className="p-2 bg-green-500/20 rounded-lg mr-3">
                <Layers className="h-5 w-5 text-green-400" />
              </div>
              Technologies
            </CardTitle>
            <CardDescription className="text-gray-400 ml-12">
              Manage your tech stack
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Technologies technologies={data.technologies} />
          </CardContent>
        </Card>

        <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm hover:bg-gray-900/70 transition-all duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-white text-lg">
              <div className="p-2 bg-orange-500/20 rounded-lg mr-3">
                <Code className="h-5 w-5 text-orange-400" />
              </div>
              Gallery
            </CardTitle>
            <CardDescription className="text-gray-400 ml-12">
              Manage your portfolio gallery
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Gallery gallery={data.gallery} />
          </CardContent>
        </Card>

        <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm hover:bg-gray-900/70 transition-all duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-white text-lg">
              <div className="p-2 bg-pink-500/20 rounded-lg mr-3">
                <Share2 className="h-5 w-5 text-pink-400" />
              </div>
              Social Links
            </CardTitle>
            <CardDescription className="text-gray-400 ml-12">
              Manage your social media links
            </CardDescription>
          </CardHeader>
          <CardContent>
            <SocialLinks socialLinks={data.socialLinks} />
          </CardContent>
        </Card>

        <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm hover:bg-gray-900/70 transition-all duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-white text-lg">
              <div className="p-2 bg-gray-500/20 rounded-lg mr-3">
                <Github className="h-5 w-5 text-gray-400" />
              </div>
              GitHub Stats
            </CardTitle>
            <CardDescription className="text-gray-400 ml-12">
              Update your GitHub statistics
            </CardDescription>
          </CardHeader>
          <CardContent>
            <GitHubStats githubStats={data.githubStats} />
          </CardContent>
        </Card>

        <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm hover:bg-gray-900/70 transition-all duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-white text-lg">Preview Site</CardTitle>
            <CardDescription className="text-gray-400">
              View your portfolio website
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-400 mb-4">
              See how your portfolio looks with the current content
            </p>
            <Button
              asChild
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white border-0"
            >
              <Link href="/" target="_blank">
                View Portfolio
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
