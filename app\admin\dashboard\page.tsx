"use client"

import Link from "next/link"
import { usePortfolio } from "@/contexts/portfolio-context"
import { useAuth } from "@/contexts/auth-context"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { User, Code, Github, Share2, Layers, ArrowRight, RefreshCcw, Shield } from "lucide-react"

export default function DashboardPage() {
  const { data, refreshData } = usePortfolio()
  const { user } = useAuth()

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">Admin Dashboard</h1>
          <p className="text-gray-400 mt-2">Manage your portfolio content</p>
        </div>
        <Button onClick={() => refreshData()} variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white" className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white">
          <RefreshCcw className="mr-2 h-4 w-4" /> Refresh Data
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card className="bg-gray-900 border-gray-800">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-white">
              <Shield className="mr-2 h-5 w-5" /> Authentication
            </CardTitle>
            <CardDescription className="text-gray-400">Your authentication status</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-300">Email:</span>
                <span className="text-sm text-gray-400">{user?.email}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-300">User ID:</span>
                <span className="text-xs font-mono text-gray-400">{user?.id?.slice(0, 8)}...</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-300">Status:</span>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-600">Authenticated</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-white">
              <User className="mr-2 h-5 w-5" /> Personal Info
            </CardTitle>
            <CardDescription className="text-gray-400">Update your personal information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-300">Name:</span>
                <span className="text-sm text-gray-400">{data.personalInfo.name}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-300">Location:</span>
                <span className="text-sm text-gray-400">{data.personalInfo.location}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-300">Website:</span>
                <span className="text-sm text-gray-400">{data.personalInfo.website}</span>
              </div>
            </div>
            <Button asChild className="w-full mt-4" variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white">
              <Link href="/admin/personal-info">
                Edit <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-white">
              <Layers className="mr-2 h-5 w-5" /> Technologies
            </CardTitle>
            <CardDescription className="text-gray-400">Manage your tech stack</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-300">Total:</span>
                <span className="text-sm text-gray-400">{data.technologies.length} technologies</span>
              </div>
              <div className="flex flex-wrap gap-1 mt-2">
                {data.technologies.slice(0, 5).map((tech) => (
                  <span key={tech} className="px-2 py-1 bg-gray-100 rounded-full text-xs">
                    {tech}
                  </span>
                ))}
                {data.technologies.length > 5 && (
                  <span className="px-2 py-1 bg-gray-100 rounded-full text-xs">
                    +{data.technologies.length - 5} more
                  </span>
                )}
              </div>
            </div>
            <Button asChild className="w-full mt-4" variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white">
              <Link href="/admin/technologies">
                Edit <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-white">
              <Code className="mr-2 h-5 w-5" /> Gallery
            </CardTitle>
            <CardDescription className="text-gray-400">Manage your portfolio gallery</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-300">Total:</span>
                <span className="text-sm text-gray-400">{data.gallery.length} gallery items</span>
              </div>
              <ul className="mt-2 space-y-1">
                {data.gallery.slice(0, 3).map((item) => (
                  <li key={item.id} className="text-sm truncate">
                    • {item.title}
                  </li>
                ))}
                {data.gallery.length > 3 && (
                  <li className="text-sm text-gray-400">+{data.gallery.length - 3} more items</li>
                )}
              </ul>
            </div>
            <Button asChild className="w-full mt-4" variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white">
              <Link href="/admin/gallery">
                Edit <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-white">
              <Share2 className="mr-2 h-5 w-5" /> Social Links
            </CardTitle>
            <CardDescription className="text-gray-400">Manage your social media links</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-300">Total:</span>
                <span className="text-sm text-gray-400">{data.socialLinks.length} links</span>
              </div>
              <ul className="mt-2 space-y-1">
                {data.socialLinks.map((link) => (
                  <li key={link.platform} className="text-sm truncate">
                    • {link.platform}
                  </li>
                ))}
              </ul>
            </div>
            <Button asChild className="w-full mt-4" variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white">
              <Link href="/admin/social-links">
                Edit <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader className="pb-2">
            <CardTitle className="flex items-center text-white">
              <Github className="mr-2 h-5 w-5" /> GitHub Stats
            </CardTitle>
            <CardDescription className="text-gray-400">Update your GitHub statistics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-300">Stars:</span>
                <span className="text-sm text-gray-400">{data.githubStats.stars}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-300">Repositories:</span>
                <span className="text-sm text-gray-400">{data.githubStats.repos}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium text-gray-300">Followers:</span>
                <span className="text-sm text-gray-400">{data.githubStats.followers}</span>
              </div>
            </div>
            <Button asChild className="w-full mt-4" variant="outline" size="sm" className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white">
              <Link href="/admin/github-stats">
                Edit <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
          </CardContent>
        </Card>

        <Card className="bg-gray-900 border-gray-800">
          <CardHeader className="pb-2">
            <CardTitle>Preview Site</CardTitle>
            <CardDescription className="text-gray-400">View your portfolio website</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-gray-400 mb-4">See how your portfolio looks with the current content</p>
            <Button asChild className="w-full">
              <Link href="/" target="_blank">
                View Portfolio
              </Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}