import { useEffect, useState } from 'react';

export interface DeviceInfo {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  userAgent: string;
  width: number;
}

const defaultBreakpoints = { mobile: 768, tablet: 1024 };

export const useDeviceDetect = (customBreakpoints?: { mobile: number; tablet: number }): DeviceInfo => {
  const breakpoints = customBreakpoints || defaultBreakpoints;

  const [deviceInfo, setDeviceInfo] = useState<DeviceInfo>(() => {
    const width = typeof window !== 'undefined' ? window.innerWidth : 0;
    return {
      isMobile: false,
      isTablet: false,
      isDesktop: false,
      userAgent: '',
      width,
    };
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const userAgent = navigator.userAgent.toLowerCase();
    const isMobileUA = /android|iphone|ipad|ipod|blackberry|iemobile|opera mini/.test(userAgent);

    const updateDevice = () => {
      const width = window.innerWidth;
      const isMobile = width <= breakpoints.mobile || isMobileUA;
      const isTablet = width > breakpoints.mobile && width <= breakpoints.tablet;
      const isDesktop = width > breakpoints.tablet;

      setDeviceInfo({
        isMobile,
        isTablet: isTablet && !isMobile,
        isDesktop: isDesktop && !isMobile && !isTablet,
        userAgent: navigator.userAgent,
        width,
      });
    };

    updateDevice();
    window.addEventListener('resize', updateDevice);

    return () => {
      window.removeEventListener('resize', updateDevice);
    };
  }, []); // ✅ Không có dependency thay đổi liên tục

  return deviceInfo;
};
