// Simple validation without external dependencies

export interface ValidationResult {
  isValid: boolean
  errors: string[]
}

// Email validation regex
const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

// URL validation regex
const urlRegex = /^https?:\/\/.+/

// Personal Info validation
export function validatePersonalInfo(data: any): ValidationResult {
  const errors: string[] = []

  if (!data.name || typeof data.name !== 'string') {
    errors.push("Name is required")
  } else if (data.name.length > 100) {
    errors.push("Name must be less than 100 characters")
  } else if (!/^[a-zA-Z\s\-'\.]+$/.test(data.name)) {
    errors.push("Name contains invalid characters")
  }

  if (!data.role || typeof data.role !== 'string') {
    errors.push("Role is required")
  } else if (data.role.length > 100) {
    errors.push("Role must be less than 100 characters")
  }

  if (!data.bio || typeof data.bio !== 'string') {
    errors.push("Bio is required")
  } else if (data.bio.length > 1000) {
    errors.push("Bio must be less than 1000 characters")
  }

  if (!data.location || typeof data.location !== 'string') {
    errors.push("Location is required")
  } else if (data.location.length > 200) {
    errors.push("Location must be less than 200 characters")
  }

  if (data.website && !urlRegex.test(data.website)) {
    errors.push("Invalid website URL")
  }

  if (!data.email || typeof data.email !== 'string') {
    errors.push("Email is required")
  } else if (!emailRegex.test(data.email)) {
    errors.push("Invalid email address")
  }

  if (data.profileImage && !urlRegex.test(data.profileImage)) {
    errors.push("Invalid profile image URL")
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Gallery Item validation
export function validateGalleryItem(data: any): ValidationResult {
  const errors: string[] = []

  if (!data.id || typeof data.id !== 'string') {
    errors.push("ID is required")
  }

  if (!data.title || typeof data.title !== 'string') {
    errors.push("Title is required")
  } else if (data.title.length > 200) {
    errors.push("Title must be less than 200 characters")
  }

  if (!data.description || typeof data.description !== 'string') {
    errors.push("Description is required")
  } else if (data.description.length > 1000) {
    errors.push("Description must be less than 1000 characters")
  }

  if (!data.imageUrl || typeof data.imageUrl !== 'string') {
    errors.push("Image URL is required")
  } else if (!urlRegex.test(data.imageUrl)) {
    errors.push("Invalid image URL")
  }

  if (!data.category || typeof data.category !== 'string') {
    errors.push("Category is required")
  } else if (data.category.length > 50) {
    errors.push("Category must be less than 50 characters")
  }

  if (!Array.isArray(data.tags)) {
    errors.push("Tags must be an array")
  } else if (data.tags.length > 10) {
    errors.push("Maximum 10 tags allowed")
  } else {
    data.tags.forEach((tag: any, index: number) => {
      if (typeof tag !== 'string') {
        errors.push(`Tag ${index + 1} must be a string`)
      } else if (tag.length > 30) {
        errors.push(`Tag ${index + 1} must be less than 30 characters`)
      }
    })
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Social Link validation
export function validateSocialLink(data: any): ValidationResult {
  const errors: string[] = []

  if (!data.platform || typeof data.platform !== 'string') {
    errors.push("Platform is required")
  } else if (data.platform.length > 50) {
    errors.push("Platform must be less than 50 characters")
  }

  if (!data.url || typeof data.url !== 'string') {
    errors.push("URL is required")
  } else if (!urlRegex.test(data.url)) {
    errors.push("Invalid URL")
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// GitHub Stats validation
export function validateGithubStats(data: any): ValidationResult {
  const errors: string[] = []

  if (typeof data.stars !== 'number' || !Number.isInteger(data.stars)) {
    errors.push("Stars must be an integer")
  } else if (data.stars < 0) {
    errors.push("Stars cannot be negative")
  } else if (data.stars > 1000000) {
    errors.push("Stars value too large")
  }

  if (typeof data.repos !== 'number' || !Number.isInteger(data.repos)) {
    errors.push("Repos must be an integer")
  } else if (data.repos < 0) {
    errors.push("Repos cannot be negative")
  } else if (data.repos > 10000) {
    errors.push("Repos value too large")
  }

  if (typeof data.followers !== 'number' || !Number.isInteger(data.followers)) {
    errors.push("Followers must be an integer")
  } else if (data.followers < 0) {
    errors.push("Followers cannot be negative")
  } else if (data.followers > 1000000) {
    errors.push("Followers value too large")
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Admin login validation
export function validateAdminLogin(data: any): ValidationResult {
  const errors: string[] = []

  if (!data.email || typeof data.email !== 'string') {
    errors.push("Email is required")
  } else if (!emailRegex.test(data.email)) {
    errors.push("Invalid email address")
  }

  if (!data.password || typeof data.password !== 'string') {
    errors.push("Password is required")
  } else if (data.password.length < 6) {
    errors.push("Password must be at least 6 characters")
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// File upload validation
export function validateFileUpload(file: File, type: string): ValidationResult {
  const errors: string[] = []

  if (!file) {
    errors.push("File is required")
  } else {
    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      errors.push("File size must be less than 5MB")
    }

    // Check file type
    if (!file.type.startsWith("image/")) {
      errors.push("File must be an image")
    }

    // Check allowed image types
    const allowedTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"]
    if (!allowedTypes.includes(file.type)) {
      errors.push("File type not supported. Use JPG, PNG, GIF, or WebP")
    }
  }

  if (!["avatar", "project", "general"].includes(type)) {
    errors.push("Invalid file type specified")
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Sanitization helpers
export function sanitizeHtml(input: string): string {
  return input
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&#x27;")
    .replace(/\//g, "&#x2F;")
}

export function sanitizeString(input: string): string {
  return input.trim().replace(/\s+/g, " ")
}

export function sanitizeFileName(input: string): string {
  return input
    .replace(/[^a-zA-Z0-9.-]/g, "_")
    .replace(/_{2,}/g, "_")
    .toLowerCase()
}

// Rate limiting helper (simple in-memory implementation)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

export function checkRateLimit(
  identifier: string,
  maxRequests: number = 10,
  windowMs: number = 60000
): boolean {
  const now = Date.now()
  const record = rateLimitMap.get(identifier)

  if (!record || now > record.resetTime) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs })
    return true
  }

  if (record.count >= maxRequests) {
    return false
  }

  record.count++
  return true
}

// Clean up old rate limit records
if (typeof window === 'undefined') { // Only run on server
  setInterval(() => {
    const now = Date.now()
    for (const [key, record] of rateLimitMap.entries()) {
      if (now > record.resetTime) {
        rateLimitMap.delete(key)
      }
    }
  }, 60000) // Clean up every minute
}

// CSRF token generation and validation
export function generateCSRFToken(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}

export function validateCSRFToken(token: string, storedToken: string): boolean {
  return token === storedToken && token.length > 10
}

// Input sanitization for different data types
export function sanitizePersonalInfo(data: any) {
  return {
    ...data,
    name: sanitizeString(data.name || ''),
    role: sanitizeString(data.role || ''),
    bio: sanitizeString(data.bio || ''),
    location: sanitizeString(data.location || ''),
    website: sanitizeString(data.website || ''),
    email: sanitizeString(data.email || '').toLowerCase(),
    profileImage: sanitizeString(data.profileImage || '')
  }
}

export function sanitizeGalleryItem(data: any) {
  return {
    ...data,
    title: sanitizeString(data.title || ''),
    description: sanitizeString(data.description || ''),
    category: sanitizeString(data.category || ''),
    tags: Array.isArray(data.tags) ? data.tags.map((tag: any) => sanitizeString(String(tag))) : []
  }
}
