"use client"

import type React from "react"

import { useState } from "react"
import { usePortfolio, type SocialLink } from "@/contexts/portfolio-context"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Pencil, Trash2, Plus } from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"

export default function SocialLinksPage() {
  const { data, updateSocialLinks } = usePortfolio()
  const [socialLinks, setSocialLinks] = useState<SocialLink[]>(data.socialLinks)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [currentLinkIndex, setCurrentLinkIndex] = useState<number | null>(null)
  const [formData, setFormData] = useState({
    platform: "",
    url: "",
  })

  const resetForm = () => {
    setFormData({
      platform: "",
      url: "",
    })
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleAddLink = () => {
    if (!formData.platform.trim() || !formData.url.trim()) return

    // Check if platform already exists
    if (socialLinks.some((link) => link.platform.toLowerCase() === formData.platform.toLowerCase())) {
      toast({
        title: "Platform already exists",
        description: `You already have a link for ${formData.platform}.`,
        variant: "destructive",
      })
      return
    }

    const newLinks = [
      ...socialLinks,
      {
        platform: formData.platform.trim(),
        url: formData.url.trim(),
      },
    ]

    setSocialLinks(newLinks)
    updateSocialLinks(newLinks)
    resetForm()
    setIsAddDialogOpen(false)
    toast({
      title: "Social link added",
      description: `Your ${formData.platform} link has been added successfully.`,
    })
  }

  const handleEditLink = (index: number) => {
    setCurrentLinkIndex(index)
    setFormData({
      platform: socialLinks[index].platform,
      url: socialLinks[index].url,
    })
    setIsEditDialogOpen(true)
  }

  const handleUpdateLink = () => {
    if (currentLinkIndex === null) return
    if (!formData.platform.trim() || !formData.url.trim()) return

    // Check if platform already exists and it's not the current one
    const isDuplicate = socialLinks.some(
      (link, index) => index !== currentLinkIndex && link.platform.toLowerCase() === formData.platform.toLowerCase(),
    )

    if (isDuplicate) {
      toast({
        title: "Platform already exists",
        description: `You already have a link for ${formData.platform}.`,
        variant: "destructive",
      })
      return
    }

    const newLinks = [...socialLinks]
    newLinks[currentLinkIndex] = {
      platform: formData.platform.trim(),
      url: formData.url.trim(),
    }

    setSocialLinks(newLinks)
    updateSocialLinks(newLinks)
    resetForm()
    setCurrentLinkIndex(null)
    setIsEditDialogOpen(false)
    toast({
      title: "Social link updated",
      description: `Your ${formData.platform} link has been updated successfully.`,
    })
  }

  const handleDeleteLink = (index: number) => {
    const newLinks = [...socialLinks]
    newLinks.splice(index, 1)
    setSocialLinks(newLinks)
    updateSocialLinks(newLinks)
    toast({
      title: "Social link deleted",
      description: "Your social link has been deleted successfully.",
    })
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Social Links</h1>
          <p className="text-gray-500 mt-2">Manage your social media links</p>
        </div>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="mr-2 h-4 w-4" /> Add Link
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Add Social Link</DialogTitle>
              <DialogDescription>Add a new social media link to your portfolio</DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="platform">Platform</Label>
                <Input
                  id="platform"
                  name="platform"
                  value={formData.platform}
                  onChange={handleChange}
                  placeholder="GitHub, Twitter, LinkedIn, etc."
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="url">URL</Label>
                <Input
                  id="url"
                  name="url"
                  value={formData.url}
                  onChange={handleChange}
                  placeholder="https://..."
                  required
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                Cancel
              </Button>
              <Button type="button" onClick={handleAddLink}>
                Add Link
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Your Social Links</CardTitle>
          <CardDescription>Manage your social media presence</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {socialLinks.map((link, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-md">
                <div>
                  <h3 className="font-medium">{link.platform}</h3>
                  <a
                    href={link.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-blue-500 hover:underline"
                  >
                    {link.url}
                  </a>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" size="sm" onClick={() => handleEditLink(index)}>
                    <Pencil className="h-4 w-4" />
                    <span className="sr-only">Edit {link.platform}</span>
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="destructive" size="sm">
                        <Trash2 className="h-4 w-4" />
                        <span className="sr-only">Delete {link.platform}</span>
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                        <AlertDialogDescription>
                          This will remove your {link.platform} link from your portfolio.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleDeleteLink(index)}>Delete</AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            ))}

            {socialLinks.length === 0 && (
              <p className="text-center text-gray-500 py-4">
                No social links added yet. Click "Add Link" to get started.
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Edit Link Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit Social Link</DialogTitle>
            <DialogDescription>Update your social media link</DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-platform">Platform</Label>
              <Input id="edit-platform" name="platform" value={formData.platform} onChange={handleChange} required />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-url">URL</Label>
              <Input id="edit-url" name="url" value={formData.url} onChange={handleChange} required />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancel
            </Button>
            <Button type="button" onClick={handleUpdateLink}>
              Update Link
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Toaster />
    </div>
  )
}
