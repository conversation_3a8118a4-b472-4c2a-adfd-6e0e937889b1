"use client"

import { useState } from "react"
import { usePortfolio } from "@/contexts/portfolio-context"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { Plus, X, Code } from "lucide-react"

export default function TechnologiesPage() {
  const { data, updateTechnologies } = usePortfolio()
  const [technologies, setTechnologies] = useState<string[]>(data.technologies || [])
  const [newTech, setNewTech] = useState("")

  const handleAddTechnology = async () => {
    if (!newTech.trim()) {
      toast({
        title: "Error",
        description: "Please enter a technology name.",
        variant: "destructive",
      })
      return
    }

    if (technologies.includes(newTech.trim())) {
      toast({
        title: "Error",
        description: "This technology already exists.",
        variant: "destructive",
      })
      return
    }

    const updatedTechnologies = [...technologies, newTech.trim()]
    setTechnologies(updatedTechnologies)
    setNewTech("")

    try {
      await updateTechnologies(updatedTechnologies)
      toast({
        title: "Technology added",
        description: "Technology has been added successfully.",
      })
    } catch (error) {
      console.error("Error adding technology:", error)
      toast({
        title: "Error",
        description: "Failed to add technology. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleRemoveTechnology = async (techToRemove: string) => {
    const updatedTechnologies = technologies.filter(tech => tech !== techToRemove)
    setTechnologies(updatedTechnologies)

    try {
      await updateTechnologies(updatedTechnologies)
      toast({
        title: "Technology removed",
        description: "Technology has been removed successfully.",
      })
    } catch (error) {
      console.error("Error removing technology:", error)
      toast({
        title: "Error",
        description: "Failed to remove technology. Please try again.",
        variant: "destructive",
      })
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">Technologies Management</h1>
          <p className="text-gray-400 mt-2">Manage your technology stack</p>
        </div>
      </div>

      <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm">
        <CardHeader className="pb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-blue-500/20 rounded-xl">
              <Code className="h-6 w-6 text-blue-400" />
            </div>
            <div>
              <CardTitle className="text-white text-xl">Add New Technology</CardTitle>
              <CardDescription className="text-gray-400">
                Add technologies to your skill set
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <Label htmlFor="new-tech" className="text-gray-300 font-medium">Technology Name</Label>
              <Input
                id="new-tech"
                value={newTech}
                onChange={(e) => setNewTech(e.target.value)}
                placeholder="e.g., React, Node.js, Python"
                className="bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20"
                onKeyDown={(e) => e.key === 'Enter' && handleAddTechnology()}
              />
            </div>
            <Button
              onClick={handleAddTechnology}
              className="mt-6 bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Plus className="h-4 w-4 mr-2" />
              Add
            </Button>
          </div>

          <div className="space-y-4">
            <Label className="text-gray-300 font-medium">Current Technologies ({technologies.length})</Label>
            <div className="flex flex-wrap gap-3">
              {technologies.map((tech) => (
                <div
                  key={tech}
                  className="inline-flex items-center bg-blue-500/20 text-blue-300 px-4 py-2 rounded-full border border-blue-500/30 hover:bg-blue-500/30 transition-colors"
                >
                  <span className="mr-2">{tech}</span>
                  <button
                    onClick={() => handleRemoveTechnology(tech)}
                    className="text-red-400 hover:text-red-300 transition-colors"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
            {technologies.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-400">No technologies added yet. Add your first technology above.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      <Toaster />
    </div>
  )
}