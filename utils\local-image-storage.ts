import { promises as fs } from 'fs'
import path from 'path'

// Create images directory if it doesn't exist
async function ensureImagesDirectory() {
  const imagesDir = path.join(process.cwd(), 'public', 'images')
  try {
    await fs.access(imagesDir)
  } catch {
    await fs.mkdir(imagesDir, { recursive: true })
  }
}

// Save uploaded file to public/images directory
export async function saveImageFile(
  file: File,
  filename: string
): Promise<{ success: boolean; url?: string; error?: string }> {
  try {
    await ensureImagesDirectory()
    
    // Convert file to buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    
    // Save to public/images directory
    const filePath = path.join(process.cwd(), 'public', 'images', filename)
    await fs.writeFile(filePath, buffer)
    
    // Return the public URL
    const publicUrl = `/images/${filename}`
    
    return {
      success: true,
      url: publicUrl
    }
  } catch (error) {
    console.error('Error saving image file:', error)
    return {
      success: false,
      error: 'Failed to save image file'
    }
  }
}

// Delete image file from public/images directory
export async function deleteImageFile(filename: string): Promise<{ success: boolean; error?: string }> {
  try {
    const filePath = path.join(process.cwd(), 'public', 'images', filename)
    await fs.unlink(filePath)
    
    return { success: true }
  } catch (error) {
    console.error('Error deleting image file:', error)
    return {
      success: false,
      error: 'Failed to delete image file'
    }
  }
}

// Get file extension from mime type
export function getFileExtension(mimeType: string): string {
  const extensions: { [key: string]: string } = {
    'image/jpeg': 'jpg',
    'image/jpg': 'jpg',
    'image/png': 'png',
    'image/gif': 'gif',
    'image/webp': 'webp'
  }
  
  return extensions[mimeType] || 'jpg'
}