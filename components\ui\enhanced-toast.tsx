"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { CheckCircle, XCircle, AlertCircle, Info, X } from "lucide-react"
import { cn } from "@/lib/utils"

export type ToastType = "success" | "error" | "warning" | "info"

interface ToastProps {
  id: string
  type: ToastType
  title: string
  description?: string
  duration?: number
  onClose: (id: string) => void
}

const toastConfig = {
  success: {
    icon: CheckCircle,
    className: "border-green-500/20 bg-green-950/50 text-green-100",
    iconClassName: "text-green-400"
  },
  error: {
    icon: XCircle,
    className: "border-red-500/20 bg-red-950/50 text-red-100",
    iconClassName: "text-red-400"
  },
  warning: {
    icon: AlertCircle,
    className: "border-yellow-500/20 bg-yellow-950/50 text-yellow-100",
    iconClassName: "text-yellow-400"
  },
  info: {
    icon: Info,
    className: "border-blue-500/20 bg-blue-950/50 text-blue-100",
    iconClassName: "text-blue-400"
  }
}

export function Toast({ 
  id, 
  type, 
  title, 
  description, 
  duration = 5000, 
  onClose 
}: ToastProps) {
  const config = toastConfig[type]
  const Icon = config.icon

  return (
    <motion.div
      initial={{ opacity: 0, y: 50, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -50, scale: 0.95 }}
      className={cn(
        "relative flex items-start gap-3 p-4 rounded-lg border backdrop-blur-sm",
        "shadow-lg max-w-md w-full",
        config.className
      )}
    >
      {/* Icon */}
      <Icon className={cn("w-5 h-5 mt-0.5 flex-shrink-0", config.iconClassName)} />
      
      {/* Content */}
      <div className="flex-1 min-w-0">
        <h4 className="font-semibold text-sm">{title}</h4>
        {description && (
          <p className="text-sm opacity-90 mt-1">{description}</p>
        )}
      </div>
      
      {/* Close button */}
      <button
        onClick={() => onClose(id)}
        className="flex-shrink-0 p-1 rounded-md hover:bg-white/10 transition-colors"
        aria-label="Close notification"
      >
        <X className="w-4 h-4" />
      </button>
      
      {/* Progress bar */}
      <motion.div
        initial={{ width: "100%" }}
        animate={{ width: "0%" }}
        transition={{ duration: duration / 1000, ease: "linear" }}
        className="absolute bottom-0 left-0 h-1 bg-current opacity-30 rounded-b-lg"
        onAnimationComplete={() => onClose(id)}
      />
    </motion.div>
  )
}

interface ToastContainerProps {
  toasts: Array<{
    id: string
    type: ToastType
    title: string
    description?: string
    duration?: number
  }>
  onClose: (id: string) => void
}

export function ToastContainer({ toasts, onClose }: ToastContainerProps) {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      <AnimatePresence mode="popLayout">
        {toasts.map((toast) => (
          <Toast
            key={toast.id}
            {...toast}
            onClose={onClose}
          />
        ))}
      </AnimatePresence>
    </div>
  )
}

// Hook for managing toasts
export function useToast() {
  const [toasts, setToasts] = useState<Array<{
    id: string
    type: ToastType
    title: string
    description?: string
    duration?: number
  }>>([])

  const addToast = (toast: Omit<ToastProps, "id" | "onClose">) => {
    const id = Math.random().toString(36).substr(2, 9)
    setToasts(prev => [...prev, { ...toast, id }])
  }

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  return {
    toasts,
    addToast,
    removeToast,
    success: (title: string, description?: string) => 
      addToast({ type: "success", title, description }),
    error: (title: string, description?: string) => 
      addToast({ type: "error", title, description }),
    warning: (title: string, description?: string) => 
      addToast({ type: "warning", title, description }),
    info: (title: string, description?: string) => 
      addToast({ type: "info", title, description }),
  }
}
