"use client"

import { motion } from "framer-motion"
import { Badge } from "@/components/ui/badge"

interface TechStackProps {
  technologies: string[]
}

export default function TechStack({ technologies }: TechStackProps) {
  // Map technologies to their corresponding colors
  const getColorForTech = (tech: string): string => {
    const techColors: Record<string, string> = {
      CSS: "bg-blue-500",
      Go: "bg-cyan-500",
      GraphQL: "bg-pink-600",
      Dart: "bg-blue-400",
      HTML5: "bg-orange-500",
      JavaScript: "bg-yellow-400 text-black",
      Markdown: "bg-gray-700",
      TypeScript: "bg-blue-600",
      AWS: "bg-orange-600",
      Firebase: "bg-yellow-500 text-black",
      Heroku: "bg-purple-600",
      Vercel: "bg-black",
      Bootstrap: "bg-purple-500",
      Chakra: "bg-teal-400 text-black",
      "Chart.js": "bg-green-500",
      Electron: "bg-blue-300 text-black",
      "Express.js": "bg-gray-800",
      Flutter: "bg-blue-400",
      jQuery: "bg-blue-500",
      JWT: "bg-red-500",
      Less: "bg-blue-600",
      MUI: "bg-blue-500",
      NPM: "bg-red-500",
      "Nest.js": "bg-red-600",
      Next: "bg-black",
      "Node.js": "bg-green-600",
      Nuxt: "bg-green-500",
      GitHub: "bg-gray-800",
      React: "bg-blue-400",
      "React Native": "bg-blue-500",
      "React Router": "bg-red-500",
      Redux: "bg-purple-600",
      Sass: "bg-pink-500",
      "Styled-components": "bg-pink-400",
      Stylus: "bg-green-500",
      Tailwindcss: "bg-cyan-500",
      "Vue.js": "bg-green-500",
      Vuetify: "bg-blue-500",
      Webpack: "bg-blue-400",
      Yarn: "bg-blue-300 text-black",
      Jenkins: "bg-red-500",
      Nginx: "bg-green-500",
      MongoDB: "bg-green-600",
      Postgres: "bg-blue-600",
      Figma: "bg-purple-500",
      Git: "bg-orange-600",
      Linux: "bg-yellow-600",
      Babel: "bg-yellow-500 text-black",
      Docker: "bg-blue-500",
      ESLint: "bg-purple-600",
      Jira: "bg-blue-500",
      Postman: "bg-orange-500",
      Swagger: "bg-green-500",
      Trello: "bg-blue-400",
    }

    return techColors[tech] || "bg-gray-500"
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.8,
      y: 20
    },
    visible: {
      opacity: 1,
      scale: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="flex flex-wrap gap-3"
    >
      {technologies.map((tech, index) => (
        <motion.div
          key={tech}
          variants={itemVariants}
          whileHover={{ 
            scale: 1.1,
            y: -2,
            transition: { duration: 0.2 }
          }}
          whileTap={{ scale: 0.95 }}
        >
          <Badge 
            className={`${getColorForTech(tech)} text-white font-medium px-4 py-2 text-sm cursor-pointer shadow-lg hover:shadow-xl transition-all duration-300 transform hover:rotate-1`}
          >
            {tech}
          </Badge>
        </motion.div>
      ))}
    </motion.div>
  )
}