"use client"
import { useState } from "react"
import { ZoomIn } from "lucide-react"
import { useIntersectionObserver } from "@/hooks/use-intersection-observer"
import { GradientText } from "@/components/animate-ui/text/gradient"
import { ShimmeringText } from "@/components/animate-ui/text/shimmering"
import { AnimatePresence, motion } from "framer-motion"
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog"
import { cn } from "@/lib/utils"

export type GalleryItem = {
  id: string
  title: string
  description: string
  imageUrl: string
  category: string
  tags: string[]
}

interface GallerySectionProps {
  galleryItems: GalleryItem[]
}

export default function GallerySectionClient({ galleryItems }: GallerySectionProps) {
  const [selectedCategory, setSelectedCategory] = useState("All")
  const [modalItem, setModalItem] = useState<GalleryItem | null>(null)
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 })

  const categories = ["All", ...Array.from(new Set(galleryItems.map(item => item.category)))]
  const filteredItems = selectedCategory === "All"
    ? galleryItems
    : galleryItems.filter(item => item.category === selectedCategory)

  if (!galleryItems || galleryItems.length === 0) {
    return (
      <section ref={ref} className="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div className="container mx-auto px-4">
          <div className={`text-center ${isIntersecting ? 'fade-in-up animate' : 'fade-in-up'}`}>
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <GradientText text="Creative Gallery" />
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Gallery is empty. Add some items from the admin panel to showcase your work.
            </p>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section ref={ref} className="py-20">
      <div className="container mx-auto px-4">
        <div className={`text-center mb-12 ${isIntersecting ? 'fade-in-up animate' : 'fade-in-up'}`}>
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <GradientText text="Creative Gallery" />
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Explore a curated selection of my creative work across different categories.
          </p>
          <div className="flex flex-wrap justify-center gap-4 mb-8">
            {categories.map(category => (
              <button
                key={category}
                className={cn(
                  "px-6 py-2 rounded-full font-semibold border transition-all duration-300",
                  selectedCategory === category
                    ? "bg-blue-600 text-white border-blue-600 shadow-lg"
                    : "bg-white text-blue-600 border-blue-300 hover:bg-blue-50"
                )}
                onClick={() => setSelectedCategory(category)}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
        <AnimatePresence mode="wait" initial={false}>
          <motion.div
            key={selectedCategory}
            initial="hidden"
            animate="visible"
            exit="exit"
            variants={{
              hidden: { opacity: 0, y: 40 },
              visible: { opacity: 1, y: 0, transition: { staggerChildren: 0.08, delayChildren: 0.05 } },
              exit: { opacity: 0, y: -40, transition: { staggerChildren: 0.05, staggerDirection: -1 } }
            }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-8 [column-fill:_balance]"
            style={{
              columnGap: '2rem',
              columnCount: 1,
              MozColumnCount: 1,
              WebkitColumnCount: 1,
            }}
          >
            {filteredItems.map((item, idx) => (
              <motion.div
                key={item.id}
                initial={{ opacity: 0, y: 40, scale: 0.96 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                transition={{ duration: 0.5, delay: idx * 0.08, ease: 'easeOut' }}
                className="mb-8 break-inside-avoid bg-gradient-to-br from-[#181c2b] via-[#232946] to-[#2d3250] rounded-3xl p-1 shadow-2xl border-none group cursor-pointer"
                onClick={() => setModalItem(item)}
              >
                <div className="bg-[#181c2b]/95 rounded-2xl p-0 h-full flex flex-col">
                  <div className="relative h-56 overflow-hidden rounded-t-2xl">
                    <img
                      src={item.imageUrl}
                      alt={item.title}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-end p-4">
                      <button className="bg-white/90 dark:bg-gray-800/90 hover:bg-white dark:hover:bg-gray-700 text-gray-800 dark:text-gray-100 p-2 rounded-full transition-all duration-300 transform hover:scale-110 shadow-lg">
                        <ZoomIn size={18} />
                      </button>
                    </div>
                  </div>
                  <div className="p-6 flex-1 flex flex-col justify-between">
                    <h3 className="text-xl font-bold mb-2 group-hover:text-blue-400 text-blue-100 transition-colors duration-300">
                      {item.title}
                    </h3>
                    <p className="text-gray-300 mb-3 line-clamp-2">
                      {item.description}
                    </p>
                    <div className="flex flex-wrap gap-2 mt-auto">
                      {item.tags.map((tag, idx) => (
                        <motion.span
                          key={tag}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.1 + idx * 0.07, duration: 0.4, ease: 'easeOut' }}
                          className="px-3 py-1 bg-blue-900/60 text-blue-200 text-xs rounded-full font-medium hover:bg-blue-800/80 transition-colors duration-300 animate-scale-in shadow-sm"
                        >
                          {tag}
                        </motion.span>
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </motion.div>
        </AnimatePresence>
        <AnimatePresence>
          {modalItem && (
            <Dialog open={!!modalItem} onOpenChange={() => setModalItem(null)}>
              <DialogContent className="max-w-2xl p-0 overflow-hidden rounded-3xl border border-blue-700/40 shadow-xl bg-[#232946] text-blue-100">
                <DialogTitle className="sr-only">{modalItem.title}</DialogTitle>
                <motion.div
                  initial={{ opacity: 0, scale: 0.92 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.92 }}
                  transition={{ duration: 0.35, ease: 'easeOut' }}
                  className="flex flex-col md:flex-row"
                >
                  <motion.div
                    initial={{ x: -40, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.1, duration: 0.5, ease: 'easeOut' }}
                    className="md:w-1/2 h-64 md:h-auto overflow-hidden flex items-center justify-center bg-white/40 dark:bg-gray-900/40 rounded-t-3xl md:rounded-l-3xl md:rounded-tr-none"
                  >
                    <img
                      src={modalItem.imageUrl}
                      alt={modalItem.title}
                      className="w-full h-full object-cover rounded-t-3xl md:rounded-l-3xl md:rounded-tr-none shadow-xl border border-gray-200 dark:border-gray-800"
                    />
                  </motion.div>
                  {/* Divider for desktop */}
                  <div className="hidden md:block w-px bg-gradient-to-b from-blue-200/40 via-blue-400/20 to-blue-200/40 mx-0" />
                  <motion.div
                    initial={{ x: 40, opacity: 0 }}
                    animate={{ x: 0, opacity: 1 }}
                    transition={{ delay: 0.18, duration: 0.5, ease: 'easeOut' }}
                    className="md:w-1/2 p-8 flex flex-col justify-center"
                  >
                    <h3 className="text-2xl font-bold mb-2">
                      <ShimmeringText text={modalItem.title} shimmeringColor="#2563eb" color={typeof window !== 'undefined' && window.matchMedia('(prefers-color-scheme: dark)').matches ? '#f1f5f9' : '#1e293b'} />
                    </h3>
                    <motion.p
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.25, duration: 0.4, ease: 'easeOut' }}
                      className="text-gray-700 dark:text-gray-300 mb-4"
                    >
                      {modalItem.description}
                    </motion.p>
                    <div className="flex flex-wrap gap-2 mb-4">
                      {modalItem.tags.map((tag, idx) => (
                        <motion.span
                          key={tag}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: 0.3 + idx * 0.07, duration: 0.4, ease: 'easeOut' }}
                          className="px-3 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded-full font-medium"
                        >
                          {tag}
                        </motion.span>
                      ))}
                    </div>
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.35, duration: 0.4, ease: 'easeOut' }}
                      className="text-sm text-gray-500 dark:text-gray-400"
                    >
                      Category: {modalItem.category}
                    </motion.div>
                  </motion.div>
                </motion.div>
              </DialogContent>
            </Dialog>
          )}
        </AnimatePresence>
      </div>
    </section>
  )
}
