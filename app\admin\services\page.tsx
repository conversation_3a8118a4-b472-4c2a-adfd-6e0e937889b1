"use client"

import { useState, useEffect } from "react"
import { usePortfolio, type ServiceCard } from "@/contexts/portfolio-context"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { Plus, Trash2, Edit, Save, X } from "lucide-react"
import LocalImageUpload from "@/components/local-image-upload"

export default function ServicesPage() {
  const { data, updateServiceCards } = usePortfolio()
  const [serviceCards, setServiceCards] = useState<ServiceCard[]>(data.serviceCards || [])
  const [editingId, setEditingId] = useState<number | null>(null)
  const [newCard, setNewCard] = useState({
    title: "",
    description: "",
    category: "",
    image: ""
  })
  const [isSaving, setIsSaving] = useState(false)

  // Update local service cards when data changes
  useEffect(() => {
    setServiceCards(data.serviceCards || [])
  }, [data.serviceCards])

  const handleAddCard = async () => {
    if (!newCard.title.trim() || !newCard.description.trim() || !newCard.category.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    const newServiceCard: ServiceCard = {
      id: Date.now(),
      title: newCard.title,
      description: newCard.description,
      category: newCard.category,
      image: newCard.image || "https://images.unsplash.com/photo-1517180102446-f3ece451e9d8?w=400&h=500&fit=crop"
    }

    const updatedCards = [...serviceCards, newServiceCard]
    setServiceCards(updatedCards)
    setNewCard({ title: "", description: "", category: "", image: "" })

    // Auto-save after adding
    try {
      await updateServiceCards(updatedCards)
      toast({
        title: "Service card added",
        description: "Your service card has been added and saved successfully.",
      })
    } catch (error) {
      console.error("Error saving service card:", error)
      toast({
        title: "Error",
        description: "Service card added but failed to save. Please click 'Save All Changes'.",
        variant: "destructive",
      })
    }
  }

  const handleEditCard = (id: number) => {
    setEditingId(id)
  }

  const handleSaveCard = async (id: number, updatedCard: Partial<ServiceCard>) => {
    const updatedCards = serviceCards.map(card => 
      card.id === id ? { ...card, ...updatedCard } : card
    )
    setServiceCards(updatedCards)
    setEditingId(null)

    // Auto-save after editing
    try {
      await updateServiceCards(updatedCards)
      toast({
        title: "Service card updated",
        description: "Your service card has been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating service card:", error)
      toast({
        title: "Error",
        description: "Failed to update service card. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleDeleteCard = async (id: number) => {
    const updatedCards = serviceCards.filter(card => card.id !== id)
    setServiceCards(updatedCards)
    
    // Auto-save after deleting
    try {
      await updateServiceCards(updatedCards)
      toast({
        title: "Service card deleted",
        description: "Your service card has been deleted successfully.",
      })
    } catch (error) {
      console.error("Error deleting service card:", error)
      toast({
        title: "Error",
        description: "Failed to delete service card. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleSaveAll = async () => {
    setIsSaving(true)
    try {
      await updateServiceCards(serviceCards)
      toast({
        title: "Service cards updated",
        description: "Your service cards have been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating service cards:", error)
      toast({
        title: "Error",
        description: "Failed to update service cards. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  const handleImageUploaded = (url: string, cardId?: number) => {
    if (cardId) {
      setServiceCards(serviceCards.map(card => 
        card.id === cardId ? { ...card, image: url } : card
      ))
    } else {
      setNewCard({ ...newCard, image: url })
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Service Cards Management</h1>
          <p className="text-gray-500 mt-2">Manage the service cards displayed in the card stack</p>
        </div>
        <Button onClick={handleSaveAll} disabled={isSaving}>
          {isSaving ? "Saving..." : "Save All Changes"}
        </Button>
      </div>

      {/* Add New Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add New Service Card
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="new-title">Title</Label>
              <Input
                id="new-title"
                value={newCard.title}
                onChange={(e) => setNewCard({ ...newCard, title: e.target.value })}
                placeholder="e.g., Web Development"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-category">Category</Label>
              <Input
                id="new-category"
                value={newCard.category}
                onChange={(e) => setNewCard({ ...newCard, category: e.target.value })}
                placeholder="e.g., Frontend"
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor="new-description">Description</Label>
            <Textarea
              id="new-description"
              value={newCard.description}
              onChange={(e) => setNewCard({ ...newCard, description: e.target.value })}
              placeholder="Brief description of the service"
              rows={3}
            />
          </div>
          <div className="space-y-2">
            <Label>Image</Label>
            <LocalImageUpload
              onImageUploaded={(url) => handleImageUploaded(url)}
              currentImageUrl={newCard.image}
            />
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={handleAddCard}>
            <Plus className="h-4 w-4 mr-2" />
            Add Service Card
          </Button>
        </CardFooter>
      </Card>

      {/* Existing Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {serviceCards.map((card) => (
          <ServiceCardEditor
            key={card.id}
            card={card}
            isEditing={editingId === card.id}
            onEdit={() => handleEditCard(card.id)}
            onSave={(updatedCard) => handleSaveCard(card.id, updatedCard)}
            onDelete={() => handleDeleteCard(card.id)}
            onCancel={() => setEditingId(null)}
            onImageUploaded={(url) => handleImageUploaded(url, card.id)}
          />
        ))}
      </div>

      {serviceCards.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No service cards yet. Add your first service card above.</p>
        </div>
      )}

      <Toaster />
    </div>
  )
}

interface ServiceCardEditorProps {
  card: ServiceCard
  isEditing: boolean
  onEdit: () => void
  onSave: (updatedCard: Partial<ServiceCard>) => void
  onDelete: () => void
  onCancel: () => void
  onImageUploaded: (url: string) => void
}

function ServiceCardEditor({ 
  card, 
  isEditing, 
  onEdit, 
  onSave, 
  onDelete, 
  onCancel,
  onImageUploaded 
}: ServiceCardEditorProps) {
  const [editData, setEditData] = useState({
    title: card.title,
    description: card.description,
    category: card.category,
    image: card.image
  })

  useEffect(() => {
    if (isEditing) {
      setEditData({
        title: card.title,
        description: card.description,
        category: card.category,
        image: card.image
      })
    }
  }, [isEditing, card])

  const handleSave = () => {
    if (!editData.title.trim() || !editData.description.trim() || !editData.category.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }
    onSave(editData)
  }

  return (
    <Card className="overflow-hidden">
      <div className="aspect-video relative">
        <img
          src={card.image}
          alt={card.title}
          className="w-full h-full object-cover"
        />
        <div className="absolute top-2 right-2 bg-white/80 backdrop-blur px-2 py-1 rounded text-xs font-medium">
          {card.category}
        </div>
      </div>
      
      <CardContent className="p-4">
        {isEditing ? (
          <div className="space-y-3">
            <Input
              value={editData.title}
              onChange={(e) => setEditData({ ...editData, title: e.target.value })}
              placeholder="Title"
            />
            <Input
              value={editData.category}
              onChange={(e) => setEditData({ ...editData, category: e.target.value })}
              placeholder="Category"
            />
            <Textarea
              value={editData.description}
              onChange={(e) => setEditData({ ...editData, description: e.target.value })}
              placeholder="Description"
              rows={3}
            />
            <div className="space-y-2">
              <Label>Image</Label>
              <LocalImageUpload
                onImageUploaded={onImageUploaded}
                currentImageUrl={card.image}
              />
            </div>
          </div>
        ) : (
          <div>
            <h3 className="font-semibold text-lg mb-2">{card.title}</h3>
            <p className="text-gray-600 text-sm">{card.description}</p>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="p-4 pt-0 flex gap-2">
        {isEditing ? (
          <>
            <Button size="sm" onClick={handleSave}>
              <Save className="h-4 w-4 mr-1" />
              Save
            </Button>
            <Button size="sm" variant="outline" onClick={onCancel}>
              <X className="h-4 w-4 mr-1" />
              Cancel
            </Button>
          </>
        ) : (
          <>
            <Button size="sm" variant="outline" onClick={onEdit}>
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
            <Button size="sm" variant="destructive" onClick={onDelete}>
              <Trash2 className="h-4 w-4 mr-1" />
              Delete
            </Button>
          </>
        )}
      </CardFooter>
    </Card>
  )
}