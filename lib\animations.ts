// Animation utilities inspired by animate-ui.com
export const animations = {
  // Fade animations
  fadeIn: "animate-in fade-in duration-700 ease-out",
  fadeInUp: "animate-in fade-in slide-in-from-bottom-4 duration-700 ease-out",
  fadeInDown: "animate-in fade-in slide-in-from-top-4 duration-700 ease-out",
  fadeInLeft: "animate-in fade-in slide-in-from-left-4 duration-700 ease-out",
  fadeInRight: "animate-in fade-in slide-in-from-right-4 duration-700 ease-out",
  
  // Scale animations
  scaleIn: "animate-in zoom-in-95 duration-500 ease-out",
  scaleInBounce: "animate-in zoom-in-95 duration-700 ease-bounce",
  
  // Slide animations
  slideInUp: "animate-in slide-in-from-bottom-full duration-500 ease-out",
  slideInDown: "animate-in slide-in-from-top-full duration-500 ease-out",
  slideInLeft: "animate-in slide-in-from-left-full duration-500 ease-out",
  slideInRight: "animate-in slide-in-from-right-full duration-500 ease-out",
  
  // Bounce animations
  bounceIn: "animate-bounce-in",
  
  // Rotation animations
  rotateIn: "animate-in spin-in-180 duration-700 ease-out",
  
  // Stagger delays for multiple elements
  delay: {
    0: "delay-0",
    100: "delay-100",
    200: "delay-200",
    300: "delay-300",
    500: "delay-500",
    700: "delay-700",
    1000: "delay-1000",
  }
}

// Custom keyframes for more complex animations
export const customAnimations = `
  @keyframes bounce-in {
    0% {
      transform: scale(0.3);
      opacity: 0;
    }
    50% {
      transform: scale(1.05);
    }
    70% {
      transform: scale(0.9);
    }
    100% {
      transform: scale(1);
      opacity: 1;
    }
  }
  
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  
  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    50% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
    }
  }
  
  @keyframes gradient-shift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }
  
  @keyframes typewriter {
    from {
      width: 0;
    }
    to {
      width: 100%;
    }
  }
  
  @keyframes blink {
    from, to {
      border-color: transparent;
    }
    50% {
      border-color: currentColor;
    }
  }
  
  .animate-bounce-in {
    animation: bounce-in 0.6s ease-out;
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }
  
  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }
  
  .animate-typewriter {
    overflow: hidden;
    border-right: 2px solid currentColor;
    white-space: nowrap;
    animation: typewriter 3s steps(40, end), blink 0.75s step-end infinite;
  }
`