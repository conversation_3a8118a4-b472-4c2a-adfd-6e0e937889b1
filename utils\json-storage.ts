import { promises as fs } from 'fs'
import path from 'path'
import type { PersonalInfo, GalleryItem, SocialLink, GithubStats, Skill, TimelineItem, ServiceCard, Stat } from '@/actions/portfolio-actions'

export interface PortfolioData {
  personalInfo: PersonalInfo
  technologies: string[]
  gallery: GalleryItem[]
  socialLinks: SocialLink[]
  githubStats: GithubStats
  aboutMe: string
  workAreas: string[]
  skills: Skill[]
  timeline: TimelineItem[]
  serviceCards: ServiceCard[]
  stats: Stat[]
}

const DATA_FILE_PATH = path.join(process.cwd(), 'data', 'portfolio.json')

// Ensure data directory exists
async function ensureDataDirectory() {
  const dataDir = path.dirname(DATA_FILE_PATH)
  try {
    await fs.access(dataDir)
  } catch {
    await fs.mkdir(dataDir, { recursive: true })
  }
}

// Read portfolio data from JSON file
export async function readPortfolioData(): Promise<PortfolioData> {
  try {
    await ensureDataDirectory()
    const data = await fs.readFile(DATA_FILE_PATH, 'utf-8')
    return JSON.parse(data)
  } catch (error) {
    console.error('Error reading portfolio data:', error)
    // Return default empty data if file doesn't exist or is corrupted
    const defaultData: PortfolioData = {
      personalInfo: {
        name: "",
        role: "",
        bio: "",
        location: "",
        website: "",
        email: "",
        profileImage: ""
      },
      technologies: [],
      gallery: [],
      socialLinks: [],
      githubStats: {
        stars: 0,
        repos: 0,
        followers: 0
      },
      aboutMe: "",
      workAreas: [],
      skills: [],
      timeline: [],
      serviceCards: [],
      stats: []
    }
    
    // Create the file with default data
    await writePortfolioData(defaultData)
    return defaultData
  }
}

// Write portfolio data to JSON file
export async function writePortfolioData(data: PortfolioData): Promise<void> {
  try {
    await ensureDataDirectory()
    await fs.writeFile(DATA_FILE_PATH, JSON.stringify(data, null, 2), 'utf-8')
  } catch (error) {
    console.error('Error writing portfolio data:', error)
    throw new Error('Failed to save portfolio data')
  }
}

// Update specific section of portfolio data
export async function updatePortfolioSection<K extends keyof PortfolioData>(
  section: K,
  data: PortfolioData[K]
): Promise<void> {
  const currentData = await readPortfolioData()
  currentData[section] = data
  await writePortfolioData(currentData)
}