"use client"

import { Suspense, lazy, ComponentType } from "react"
import { motion } from "framer-motion"

interface LazySectionProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  className?: string
}

const DefaultFallback = () => (
  <motion.div
    initial={{ opacity: 0 }}
    animate={{ opacity: 1 }}
    className="flex items-center justify-center py-20"
  >
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
  </motion.div>
)

export default function LazySection({ 
  children, 
  fallback = <DefaultFallback />, 
  className = "" 
}: LazySectionProps) {
  return (
    <Suspense fallback={fallback}>
      <div className={className}>
        {children}
      </div>
    </Suspense>
  )
}

// Helper function to create lazy components
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>
) {
  return lazy(importFn)
}
