{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "analyze": "ANALYZE=true next build"}, "dependencies": {"@next/bundle-analyzer": "^15.3.4", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "latest", "lucide-react": "^0.523.0", "motion": "^12.19.2", "next": "15.2.4", "react": "^19", "react-dom": "^19", "react-use-measure": "^2.1.7", "resend": "^4.6.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "zod": "^3.25.73"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}