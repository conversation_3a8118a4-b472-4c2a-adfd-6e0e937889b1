"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Menu, X, Home, User, Code, Image, Mail, ChevronRight } from "lucide-react"
import { RippleButton } from "@/components/animate-ui/buttons/ripple"
import { AnimatePresence, motion } from "framer-motion"
import { useDeviceDetect } from "@/hooks/use-device-detect"

export default function Navigation() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { isMobile } = useDeviceDetect()

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const navItems = [
    { href: "#home", label: "Home", icon: Home },
    { href: "#about", label: "About", icon: User },
    { href: "#projects", label: "Projects", icon: Code },
    { href: "#gallery", label: "Gallery", icon: Image },
    { href: "#contact", label: "Contact", icon: Mail },
  ]

  return (
    <>
      <nav
        role="navigation"
        aria-label="Main navigation"
        className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${
          isScrolled
            ? "bg-gray-900/95 backdrop-blur-md shadow-lg py-4 border-b border-gray-800"
            : "bg-gray-950/90 py-6"
        }`}
      >
        <div className="container mx-auto px-6 flex justify-between items-center">
          {/* Logo */}
          <div>
            <Link
              href="/"
              className="text-2xl font-bold gradient-text text-white focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg px-2 py-1"
              aria-label="Go to homepage"
            >
              Portfolio.
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-2 text-gray-200">
            {navItems.map((item) => (
              <RippleButton
                key={item.href}
                variant="ghost"
                size="default"
                className="relative group font-medium px-4 py-2 rounded-lg transition-all duration-200 text-gray-200 hover:text-blue-400 focus-visible:ring-2 focus-visible:ring-blue-500/60 focus-visible:outline-none bg-gray-900/70 hover:bg-gray-800/80"
                onClick={() => {
                  const el = document.querySelector(item.href)
                  if (el) el.scrollIntoView({ behavior: 'smooth' })
                }}
                aria-label={`Navigate to ${item.label} section`}
              >
                <span className="flex items-center gap-2">
                  {item.icon && <item.icon size={18} className="opacity-70" />}
                  <span>{item.label}</span>
                  <ChevronRight size={16} className="ml-1 opacity-0 group-hover:opacity-80 group-hover:translate-x-1 transition-all duration-200" />
                </span>
                <span className="absolute left-1/2 -bottom-1 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 group-hover:w-3/4 transition-all duration-300 rounded-full" style={{transform: 'translateX(-50%)'}}></span>
              </RippleButton>
            ))}
          </div>

          {/* Mobile Menu Button */}
          <RippleButton
            className="md:hidden p-2 rounded-lg bg-gray-900/80 text-gray-200 hover:bg-gray-800/90"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            variant="ghost"
            size="icon"
            aria-label={isMobileMenuOpen ? "Close mobile menu" : "Open mobile menu"}
            aria-expanded={isMobileMenuOpen}
            aria-controls="mobile-menu"
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </RippleButton>
        </div>
      </nav>

      <AnimatePresence>
        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <div
            id="mobile-menu"
            className="fixed top-0 right-0 h-full w-80 bg-gray-950 shadow-2xl z-40 md:hidden animate-fade-in-up border-l border-gray-800"
            role="dialog"
            aria-modal="true"
            aria-label="Mobile navigation menu"
          >
            <div className="p-6 pt-20">
              <nav role="navigation" aria-label="Mobile navigation">
                <div className="space-y-3">
                {navItems.map((item) => (
                  <RippleButton
                    key={item.href}
                    variant="ghost"
                    size="default"
                    className="flex items-center gap-3 text-lg font-medium w-full p-3 rounded-lg text-gray-200 hover:text-blue-400 bg-gray-900/70 hover:bg-gray-800/80 transition-all duration-200 focus-visible:ring-2 focus-visible:ring-blue-500/60 focus-visible:outline-none"
                    onClick={() => {
                      setIsMobileMenuOpen(false)
                      const el = document.querySelector(item.href)
                      if (el) el.scrollIntoView({ behavior: 'smooth' })
                    }}
                    aria-label={`Navigate to ${item.label} section`}
                  >
                    <item.icon size={20} className="opacity-70" />
                    <span>{item.label}</span>
                    <ChevronRight size={18} className="ml-auto opacity-0 group-hover:opacity-80 group-hover:translate-x-1 transition-all duration-200" />
                  </RippleButton>
                ))}
                </div>
              </nav>
            </div>
          </div>
        )}
      </AnimatePresence>
    </>
  )
}