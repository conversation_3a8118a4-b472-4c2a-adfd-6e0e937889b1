"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Menu, X, Home, User, Code, Image, Mail, ChevronRight } from "lucide-react"
import { RippleButton } from "@/components/animate-ui/buttons/ripple"
import { AnimatePresence, motion } from "framer-motion"
import { useDeviceDetect } from "@/hooks/use-device-detect"

export default function Navigation() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { isMobile } = useDeviceDetect()

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50)
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  // Lock body scroll when mobile menu is open
  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset'
    }
  }, [isMobileMenuOpen])

  const navItems = [
    { href: "#home", label: "Home", icon: Home },
    { href: "#about", label: "About", icon: User },
    { href: "#projects", label: "Projects", icon: Code },
    { href: "#gallery", label: "Gallery", icon: Image },
    { href: "#contact", label: "Contact", icon: Mail },
  ]

  return (
    <>
      <nav
        role="navigation"
        aria-label="Main navigation"
        className={`fixed top-0 left-0 w-full z-50 transition-all duration-300 ${
          isScrolled
            ? "bg-gray-900/95 backdrop-blur-md shadow-lg py-3 md:py-4 border-b border-gray-800"
            : "bg-gray-950/90 py-4 md:py-6"
        }`}
      >
        <div className="container mx-auto px-6 flex justify-between items-center">
          {/* Logo */}
          <div>
            <Link
              href="/"
              className="text-2xl font-bold gradient-text text-white focus:outline-none focus:ring-2 focus:ring-blue-500 rounded-lg px-2 py-1"
              aria-label="Go to homepage"
            >
              Portfolio.
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-2 text-gray-200">
            {navItems.map((item) => (
              <RippleButton
                key={item.href}
                variant="ghost"
                size="default"
                className="relative group font-medium px-4 py-2 rounded-lg transition-all duration-200 text-gray-200 hover:text-blue-400 focus-visible:ring-2 focus-visible:ring-blue-500/60 focus-visible:outline-none bg-gray-900/70 hover:bg-gray-800/80"
                onClick={() => {
                  const el = document.querySelector(item.href)
                  if (el) {
                    const navHeight = 80 // Navigation height
                    const elementPosition = el.getBoundingClientRect().top + window.pageYOffset
                    const offsetPosition = elementPosition - navHeight

                    window.scrollTo({
                      top: offsetPosition,
                      behavior: 'smooth'
                    })
                  }
                }}
                aria-label={`Navigate to ${item.label} section`}
              >
                <span className="flex items-center gap-2">
                  {item.icon && <item.icon size={18} className="opacity-70" />}
                  <span>{item.label}</span>
                  <ChevronRight size={16} className="ml-1 opacity-0 group-hover:opacity-80 group-hover:translate-x-1 transition-all duration-200" />
                </span>
                <span className="absolute left-1/2 -bottom-1 w-0 h-0.5 bg-gradient-to-r from-blue-500 to-purple-500 group-hover:w-3/4 transition-all duration-300 rounded-full" style={{transform: 'translateX(-50%)'}}></span>
              </RippleButton>
            ))}
          </div>

          {/* Mobile Menu Button */}
          <motion.div className="md:hidden">
            <RippleButton
              className="relative p-3 rounded-xl bg-gray-900/80 text-gray-200 hover:bg-gray-800/90 border border-gray-700/50 hover:border-gray-600/50 transition-all duration-200"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              variant="ghost"
              size="icon"
              aria-label={isMobileMenuOpen ? "Close mobile menu" : "Open mobile menu"}
              aria-expanded={isMobileMenuOpen}
              aria-controls="mobile-menu"
            >
              <motion.div
                animate={{ rotate: isMobileMenuOpen ? 180 : 0 }}
                transition={{ duration: 0.3 }}
              >
                {isMobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
              </motion.div>
            </RippleButton>
          </motion.div>
        </div>
      </nav>

      <AnimatePresence>
        {/* Mobile Menu Overlay */}
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-30 md:hidden"
            onClick={() => setIsMobileMenuOpen(false)}
          />
        )}
      </AnimatePresence>

      <AnimatePresence>
        {/* Mobile Menu */}
          {isMobileMenuOpen && (
            <motion.div
              id="mobile-menu"
              initial={{ x: "100%" }}
              animate={{ x: 0 }}
              exit={{ x: "100%" }}
              transition={{ type: "spring", damping: 25, stiffness: 200 }}
              className="fixed top-0 right-0 h-full w-80 max-w-[85vw] bg-gray-950/95 backdrop-blur-xl shadow-2xl z-40 md:hidden border-l border-gray-800/50"
              role="dialog"
              aria-modal="true"
              aria-label="Mobile navigation menu"
            >
            <div className="flex flex-col h-full">
              {/* Mobile Header */}
              <div className="flex items-center justify-between p-6 border-b border-gray-800/50">
                <span className="text-xl font-bold text-white">Menu</span>
                <button
                  onClick={() => setIsMobileMenuOpen(false)}
                  className="p-2 rounded-lg hover:bg-gray-800/50 transition-colors"
                  aria-label="Close menu"
                >
                  <X size={24} className="text-gray-400" />
                </button>
              </div>

              {/* Navigation Items */}
              <nav role="navigation" aria-label="Mobile navigation" className="flex-1 p-6">
                <div className="space-y-2">
                {navItems.map((item, index) => (
                  <motion.div
                    key={item.href}
                    initial={{ opacity: 0, x: 50 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 + 0.2, duration: 0.3 }}
                  >
                    <RippleButton
                      variant="ghost"
                      size="default"
                      className="flex items-center gap-4 text-base font-medium w-full p-4 rounded-xl text-gray-200 hover:text-blue-400 hover:bg-gray-800/50 transition-all duration-200 focus-visible:ring-2 focus-visible:ring-blue-500/60 focus-visible:outline-none group"
                      onClick={() => {
                        setIsMobileMenuOpen(false)
                        const el = document.querySelector(item.href)
                        if (el) {
                          const navHeight = 80
                          const elementPosition = el.getBoundingClientRect().top + window.pageYOffset
                          const offsetPosition = elementPosition - navHeight

                          window.scrollTo({
                            top: offsetPosition,
                            behavior: 'smooth'
                          })
                        }
                      }}
                      aria-label={`Navigate to ${item.label} section`}
                    >
                      <item.icon size={20} className="text-blue-400 group-hover:scale-110 transition-transform duration-200" />
                      <span className="group-hover:translate-x-1 transition-transform duration-200">{item.label}</span>
                      <ChevronRight size={16} className="ml-auto opacity-50 group-hover:opacity-100 group-hover:translate-x-1 transition-all duration-200" />
                    </RippleButton>
                  </motion.div>
                ))}
                </div>
              </nav>

              {/* Mobile Menu Footer */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.3 }}
                className="p-6 border-t border-gray-800/50"
              >
                <div className="text-center text-sm text-gray-400">
                  <p>© 2024 Portfolio</p>
                  <p className="mt-1">Made with ❤️</p>
                </div>
              </motion.div>
            </div>
            </motion.div>
          )}
        </AnimatePresence>
      </>
  )
}