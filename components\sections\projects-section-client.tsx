"use client"
import { motion, type Variants } from "framer-motion"
import { Code, ExternalLink, Github } from "lucide-react"
import { useIntersectionObserver } from "@/hooks/use-intersection-observer"

// Define Project type locally to avoid import from server or export * files
export type Project = {
  id: string;
  title: string;
  description: string;
  imageUrl?: string;
  tags: string[];
}

interface ProjectsSectionProps {
  projects: Project[]
}

const containerVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2
    }
  }
}

const itemVariants: Variants = {
  hidden: { y: 50, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.6,
      ease: "easeOut"
    }
  }
}

export default function ProjectsSectionClient({ projects }: ProjectsSectionProps) {
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 })

  return (
    <section id="projects" ref={ref} className="py-24 bg-gradient-to-br from-gray-50 to-white">
      <div className="container mx-auto px-6 md:px-12 lg:px-24">
        {/* Section Header */}
        <div className={`text-center mb-16 ${isIntersecting ? 'fade-in-up animate' : 'fade-in-up'}`}>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 flex items-center justify-center">
            <Code className="mr-4 h-8 w-8 text-blue-600 animate-bounce-in" />
            <span className="gradient-text">Featured Projects</span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover my latest work and creative solutions that bring ideas to life
          </p>
        </div>

        {/* Projects Grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate={isIntersecting ? "visible" : "hidden"}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {projects.map((project, index) => (
            <motion.div
              key={project.id}
              variants={itemVariants}
              className="group"
            >
              <div className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2">
                {/* Project Image */}
                <div className="relative overflow-hidden h-48">
                  <img
                    src={project.imageUrl || "https://images.unsplash.com/photo-1517180102446-f3ece451e9d8?w=500&h=300&fit=crop"}
                    alt={project.title}
                    className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                    <div className="absolute bottom-4 left-4 right-4 flex gap-2">
                      <button className="bg-white/90 hover:bg-white text-gray-800 p-2 rounded-full transition-all duration-300 transform hover:scale-110">
                        <ExternalLink size={16} />
                      </button>
                      <button className="bg-white/90 hover:bg-white text-gray-800 p-2 rounded-full transition-all duration-300 transform hover:scale-110">
                        <Github size={16} />
                      </button>
                    </div>
                  </div>
                </div>

                {/* Project Content */}
                <div className="p-6">
                  <h3 className="text-xl font-bold mb-3 group-hover:text-blue-600 transition-colors duration-300">
                    {project.title}
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {project.description}
                  </p>

                  {/* Tags */}
                  <div className="flex flex-wrap gap-2">
                    {project.tags.map((tag, tagIndex) => (
                      <span
                        key={tag}
                        className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full font-medium hover:bg-blue-200 transition-colors duration-300 animate-scale-in"
                        style={{ animationDelay: `${tagIndex * 0.1}s` }}
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Hover Effect Border */}
                <div className="absolute inset-0 border-2 border-transparent group-hover:border-blue-500 rounded-2xl transition-all duration-300 pointer-events-none"></div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* View More Button */}
        {projects.length > 6 && (
          <div className={`text-center mt-12 ${isIntersecting ? 'fade-in-up animate' : 'fade-in-up'}`} style={{ animationDelay: '0.8s' }}>
            <button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 rounded-full font-semibold shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 animate-glow">
              View All Projects
            </button>
          </div>
        )}
      </div>
    </section>
  )
}
