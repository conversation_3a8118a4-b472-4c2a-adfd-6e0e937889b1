import Navigation from "@/components/layout/navigation"
import HeroSection from "@/components/sections/hero-section"
import LazySection from "@/components/lazy-section"
import SkipLink from "@/components/skip-link"
import portfolioData from "@/data/portfolio.json"
import { mapTimelineItemsFromJson } from "@/lib/timeline-mapper"

// Import sections normally for server components
import AboutSection from "@/components/sections/about-section"
import TimelineSection from "@/components/sections/timeline-section"
import GithubStatsSection from "@/components/sections/github-stats-section"
import GallerySection from "@/components/sections/gallery-section"
import ContactSection from "@/components/sections/contact-section"
import Footer from "@/components/layout/footer"

export default function Home() {
  const { personalInfo, technologies, gallery, socialLinks, githubStats, skills, timeline } = portfolioData
  const timelineTyped = mapTimelineItemsFromJson(timeline)
  return (
    <>
      <SkipLink />
      <Navigation />
      <main
        id="main-content"
        className="min-h-screen text-white overflow-x-hidden"
        tabIndex={-1}
      >

      <HeroSection personalInfo={personalInfo} />

      <AboutSection personalInfo={personalInfo} technologies={technologies} skills={skills || []} />

      <LazySection>
        <TimelineSection timeline={timelineTyped || []} />
      </LazySection>

      <LazySection>
        <GithubStatsSection githubStats={githubStats} />
      </LazySection>

      <LazySection>
        <GallerySection galleryItems={gallery || []} />
      </LazySection>

      <LazySection>
        <ContactSection personalInfo={personalInfo} socialLinks={socialLinks} />
      </LazySection>

      <LazySection>
        <Footer personalInfo={personalInfo} socialLinks={socialLinks} />
      </LazySection>
      </main>
    </>
  )
}