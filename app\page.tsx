import Navigation from "@/components/layout/navigation"
import HeroSection from "@/components/sections/hero-section"
import AboutSection from "@/components/sections/about-section"
import GithubStatsSection from "@/components/sections/github-stats-section"
import GallerySection from "@/components/sections/gallery-section"
import ContactSection from "@/components/sections/contact-section"
import Footer from "@/components/layout/footer"
import TimelineSection from "@/components/sections/timeline-section"
import portfolioData from "@/data/portfolio.json"
import { mapTimelineItemsFromJson } from "@/lib/timeline-mapper"

export default function Home() {
  const { personalInfo, technologies, gallery, socialLinks, githubStats, skills, timeline } = portfolioData
  const timelineTyped = mapTimelineItemsFromJson(timeline)
  return (
    <main className="min-h-screen text-white overflow-x-hidden">
      <Navigation />

      <HeroSection personalInfo={personalInfo} />

      <AboutSection personalInfo={personalInfo} technologies={technologies} skills={skills || []} />

      <TimelineSection timeline={timelineTyped || []} />

      <GithubStatsSection githubStats={githubStats} />

      <GallerySection galleryItems={gallery || []} />

      <ContactSection personalInfo={personalInfo} socialLinks={socialLinks} />

      <Footer personalInfo={personalInfo} socialLinks={socialLinks} />
    </main>
  )
}