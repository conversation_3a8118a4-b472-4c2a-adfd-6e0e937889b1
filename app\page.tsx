import dynamic from "next/dynamic"
import Navigation from "@/components/layout/navigation"
import HeroSection from "@/components/sections/hero-section"
import LazySection from "@/components/lazy-section"
import portfolioData from "@/data/portfolio.json"
import { mapTimelineItemsFromJson } from "@/lib/timeline-mapper"

// Lazy load non-critical sections
const AboutSection = dynamic(() => import("@/components/sections/about-section"), {
  ssr: true,
})
const TimelineSection = dynamic(() => import("@/components/sections/timeline-section"), {
  ssr: false,
})
const GithubStatsSection = dynamic(() => import("@/components/sections/github-stats-section"), {
  ssr: false,
})
const GallerySection = dynamic(() => import("@/components/sections/gallery-section"), {
  ssr: false,
})
const ContactSection = dynamic(() => import("@/components/sections/contact-section"), {
  ssr: false,
})
const Footer = dynamic(() => import("@/components/layout/footer"), {
  ssr: false,
})

export default function Home() {
  const { personalInfo, technologies, gallery, socialLinks, githubStats, skills, timeline } = portfolioData
  const timelineTyped = mapTimelineItemsFromJson(timeline)
  return (
    <main className="min-h-screen text-white overflow-x-hidden">
      <Navigation />

      <HeroSection personalInfo={personalInfo} />

      <AboutSection personalInfo={personalInfo} technologies={technologies} skills={skills || []} />

      <LazySection>
        <TimelineSection timeline={timelineTyped || []} />
      </LazySection>

      <LazySection>
        <GithubStatsSection githubStats={githubStats} />
      </LazySection>

      <LazySection>
        <GallerySection galleryItems={gallery || []} />
      </LazySection>

      <LazySection>
        <ContactSection personalInfo={personalInfo} socialLinks={socialLinks} />
      </LazySection>

      <LazySection>
        <Footer personalInfo={personalInfo} socialLinks={socialLinks} />
      </LazySection>
    </main>
  )
}