import { GalleryItem } from "@/actions/portfolio-actions";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

interface GalleryProps {
    gallery: GalleryItem[];
}

export default function Gallery({ gallery }: GalleryProps) {
  return (
    <>
      <div className="space-y-3">
        <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
          <span className="text-sm font-medium text-gray-300">Total</span>
          <span className="text-sm text-white">
            {gallery.length} gallery items
          </span>
        </div>
        <ul className="mt-2 space-y-1 text-white">
          {gallery.slice(0, 3).map((item) => (
            <li key={item.id} className="text-sm truncate">
              • {item.title}
            </li>
          ))}
          {gallery.length > 3 && (
            <li className="text-sm text-gray-400">
              +{gallery.length - 3} more items
            </li>
          )}
        </ul>
      </div>
      <Button
        asChild
        className="w-full mt-4 bg-orange-600 hover:bg-orange-700 text-white border-0"
        size="sm"
      >
        <Link href="/admin/gallery">
          Edit <ArrowRight className="ml-2 h-4 w-4" />
        </Link>
      </Button>
    </>
  );
}
