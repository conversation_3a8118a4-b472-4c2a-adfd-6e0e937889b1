"use client"

import type React from "react"

import { useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import Link from "next/link"
import { useAuth } from "@/contexts/auth-context"
import { AuthProvider } from "@/contexts/auth-context"
import { PortfolioProvider } from "@/contexts/portfolio-context"
import { Button } from "@/components/ui/button"
import {
  User,
  Code,
  Github,
  LogOut,
  Share2,
  LayoutDashboard,
  Layers,
  Briefcase,
  Settings,
  TrendingUp,
} from "lucide-react"

export default function AdminLayout({ children }: { children: React.ReactNode }) {
  return (
    <AuthProvider>
      <PortfolioProvider>
        <AdminLayoutContent>{children}</AdminLayoutContent>
      </PortfolioProvider>
    </AuthProvider>
  )
}

function AdminLayoutContent({ children }: { children: React.ReactNode }) {
  const { user, logout, isLoading } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    if (!isLoading && !user && pathname !== "/admin/login") {
      router.push("/admin/login")
    }
  }, [user, isLoading, router, pathname])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-950 text-white">
        <div className="flex flex-col items-center gap-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  if (!user && pathname !== "/admin/login") {
    return null
  }

  if (pathname === "/admin/login") {
    return children
  }

  return (
    <div className="min-h-screen bg-gray-950">
      <div className="flex h-screen overflow-hidden">
        {/* Sidebar */}
        <div className="hidden md:flex md:w-64 md:flex-col">
          <div className="flex flex-col flex-grow pt-5 overflow-y-auto bg-gray-900 border-r border-gray-800">
            <div className="px-4 pb-2 flex items-center">
              <h1 className="text-xl font-semibold text-white">Portfolio Admin</h1>
            </div>
            <div className="px-4 py-2">
              <p className="text-sm text-gray-400">Logged in as {user?.email}</p>
              <div className="flex items-center gap-2 mt-1">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-xs text-green-400">Authenticated</span>
              </div>
            </div>
            <div className="mt-5 flex-1 flex flex-col">
              <nav className="flex-1 px-2 space-y-1">
                <Link
                  href="/admin/dashboard"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                    pathname === "/admin/dashboard"
                      ? "bg-blue-600 text-white shadow-lg"
                      : "text-gray-300 hover:bg-gray-800 hover:text-white"
                  }`}
                >
                  <LayoutDashboard className="mr-3 h-5 w-5" />
                  Dashboard
                </Link>
                <Link
                  href="/admin/personal-info"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                    pathname === "/admin/personal-info"
                      ? "bg-blue-600 text-white shadow-lg"
                      : "text-gray-300 hover:bg-gray-800 hover:text-white"
                  }`}
                >
                  <User className="mr-3 h-5 w-5" />
                  Personal Info
                </Link>
                <Link
                  href="/admin/technologies"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                    pathname === "/admin/technologies"
                      ? "bg-blue-600 text-white shadow-lg"
                      : "text-gray-300 hover:bg-gray-800 hover:text-white"
                  }`}
                >
                  <Layers className="mr-3 h-5 w-5" />
                  Technologies
                </Link>
                <Link
                  href="/admin/gallery"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 ${
                    pathname === "/admin/gallery"
                      ? "bg-blue-600 text-white shadow-lg"
                      : "text-gray-300 hover:bg-gray-800 hover:text-white"
                  }`}
                >
                  <Code className="mr-3 h-5 w-5" />
                  Gallery
                </Link>
                <Link
                  href="/admin/social-links"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 ${pathname === "/admin/social-links" ? "bg-blue-600 text-white shadow-lg" : "text-gray-300 hover:bg-gray-800 hover:text-white"}`}
                >
                  <Share2 className="mr-3 h-5 w-5" />
                  Social Links
                </Link>
                <Link
                  href="/admin/github-stats"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 ${pathname === "/admin/github-stats" ? "bg-blue-600 text-white shadow-lg" : "text-gray-300 hover:bg-gray-800 hover:text-white"}`}
                >
                  <Github className="mr-3 h-5 w-5" />
                  GitHub Stats
                </Link>
                <Link
                  href="/admin/timeline"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 ${pathname === "/admin/timeline" ? "bg-blue-600 text-white shadow-lg" : "text-gray-300 hover:bg-gray-800 hover:text-white"}`}
                >
                  <Briefcase className="mr-3 h-5 w-5" />
                  Timeline
                </Link>
                <Link
                  href="/admin/skills"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 ${pathname === "/admin/skills" ? "bg-blue-600 text-white shadow-lg" : "text-gray-300 hover:bg-gray-800 hover:text-white"}`}
                >
                  <Layers className="mr-3 h-5 w-5" />
                  Skills
                </Link>
                <Link
                  href="/admin/services"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 ${pathname === "/admin/services" ? "bg-blue-600 text-white shadow-lg" : "text-gray-300 hover:bg-gray-800 hover:text-white"}`}
                >
                  <Settings className="mr-3 h-5 w-5" />
                  Services
                </Link>
                <Link
                  href="/admin/stats"
                  className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-all duration-200 ${pathname === "/admin/stats" ? "bg-blue-600 text-white shadow-lg" : "text-gray-300 hover:bg-gray-800 hover:text-white"}`}
                >
                  <TrendingUp className="mr-3 h-5 w-5" />
                  Stats
                </Link>
              </nav>
            </div>
            <div className="p-4">
              <Button
                variant="outline"
                className="w-full justify-start border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white hover:border-gray-600"
                onClick={() => {
                  logout()
                  router.push("/admin/login")
                }}
              >
                <LogOut className="mr-2 h-4 w-4" />
                Logout
              </Button>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="flex flex-col flex-1 overflow-hidden bg-gray-950">
          <main className="flex-1 relative overflow-y-auto focus:outline-none p-6 text-white">{children}</main>
        </div>
      </div>
    </div>
  )
}