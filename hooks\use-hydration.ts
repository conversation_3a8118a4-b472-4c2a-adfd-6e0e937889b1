import { useState, useEffect } from 'react'

/**
 * Hook to safely handle hydration mismatches
 * Returns true only after the component has hydrated on the client
 */
export function useHydration() {
  const [isHydrated, setIsHydrated] = useState(false)

  useEffect(() => {
    setIsHydrated(true)
  }, [])

  return isHydrated
}

/**
 * Hook to safely access window object
 * Returns window object only after hydration
 */
export function useSafeWindow() {
  const isHydrated = useHydration()
  
  return isHydrated && typeof window !== 'undefined' ? window : null
}

/**
 * Hook for client-side only rendering
 * Useful for components that should only render on the client
 */
export function useClientOnly() {
  return useHydration()
}