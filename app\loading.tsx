import { Loader2, <PERSON><PERSON><PERSON>, <PERSON>, Palette } from "lucide-react";

export default function Loading() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-200 via-white to-purple-200 relative overflow-hidden">
      {/* Floating creative icons */}
      <Sparkles className="absolute left-10 top-16 text-yellow-400 opacity-60 animate-float-slow" style={{fontSize: 60}} />
      <Code className="absolute right-16 top-32 text-blue-400 opacity-50 animate-float" style={{fontSize: 48}} />
      <Palette className="absolute left-24 bottom-24 text-purple-400 opacity-50 animate-float-reverse" style={{fontSize: 56}} />
      {/* Animated loader */}
      <div className="relative mb-8 z-10">
        <span className="absolute -top-8 -left-8 w-24 h-24 bg-blue-300 rounded-full blur-2xl opacity-30 animate-pulse"></span>
        <Loader2 className="h-24 w-24 text-blue-600 animate-spin drop-shadow-xl" />
        <span className="absolute -bottom-8 -right-8 w-24 h-24 bg-purple-300 rounded-full blur-2xl opacity-30 animate-pulse"></span>
      </div>
      <div className="flex gap-2 mt-4 animate-fade-in-up">
        <span className="inline-block w-3 h-3 bg-blue-400 rounded-full animate-bounce [animation-delay:.1s]"></span>
        <span className="inline-block w-3 h-3 bg-purple-400 rounded-full animate-bounce [animation-delay:.2s]"></span>
        <span className="inline-block w-3 h-3 bg-yellow-400 rounded-full animate-bounce [animation-delay:.3s]"></span>
      </div>
      {/* Custom creative CSS animations moved to globals.css */}
    </div>
  );
}
