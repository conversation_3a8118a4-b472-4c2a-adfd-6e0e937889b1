import Link from "next/link";
import { <PERSON>, ArrowLeft } from "lucide-react";

export default function NotFound() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-pink-100 via-white to-blue-100 animate-fade-in">
      <div className="relative mb-6">
        <Ghost className="h-24 w-24 text-pink-400 animate-bounce drop-shadow-lg" />
        <span className="absolute -top-6 -left-6 w-20 h-20 bg-pink-200 rounded-full blur-2xl opacity-30 animate-pulse"></span>
        <span className="absolute -bottom-6 -right-6 w-20 h-20 bg-blue-200 rounded-full blur-2xl opacity-30 animate-pulse"></span>
      </div>
      <h1 className="text-5xl font-extrabold text-pink-600 mb-2 animate-typewriter">404</h1>
      <h2 className="text-2xl font-bold text-gray-700 mb-4 animate-fade-in-up">Page Not Found</h2>
      <p className="text-lg text-gray-500 mb-8 animate-fade-in-up">Sorry, the page you are looking for does not exist or has been moved.</p>
      <Link href="/" className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg shadow hover:bg-blue-700 transition-colors animate-fade-in-up">
        <ArrowLeft className="mr-2 h-5 w-5" /> Go Home
      </Link>
    </div>
  );
}
