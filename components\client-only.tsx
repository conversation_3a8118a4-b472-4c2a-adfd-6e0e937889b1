"use client"

import { useClientOnly } from "@/hooks/use-hydration"

interface ClientOnlyProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

/**
 * Component wrapper that only renders children on the client side
 * Useful for preventing hydration mismatches
 */
export default function ClientOnly({ children, fallback = null }: ClientOnlyProps) {
  const isClientOnly = useClientOnly()

  if (!isClientOnly) {
    return <>{fallback}</>
  }

  return <>{children}</>
}