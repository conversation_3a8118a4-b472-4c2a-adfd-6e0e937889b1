
'use client'
import { Mail, Github, Globe, Share2, Send, MapPin } from "lucide-react"
import { useIntersectionObserver } from "@/hooks/use-intersection-observer"
import ContactForm from "@/components/ui/contact-form"
import { GradientText } from "@/components/animate-ui/text/gradient"
import { type PersonalInfo, type SocialLink } from "@/actions/portfolio-actions"
import { motion } from "framer-motion"

interface ContactSectionProps {
  personalInfo: PersonalInfo
  socialLinks: SocialLink[]
}

export default function ContactSection({ personalInfo, socialLinks }: ContactSectionProps) {
  const { ref, isIntersecting } = useIntersectionObserver({ threshold: 0.1 })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { y: 30, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  const getSocialIcon = (platform: string) => {
    switch (platform.toLowerCase()) {
      case "github":
        return Github
      case "website":
        return Globe
      default:
        return Share2
    }
  }

  return (
    <motion.section
      initial={{ opacity: 0, y: 40 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, amount: 0.2 }}
      transition={{ duration: 0.7, ease: "easeOut" }}
      className="relative py-24 bg-gray-950 text-white"
    >
      {/* Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-900 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-float"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-900 rounded-full mix-blend-multiply filter blur-xl opacity-30 animate-float" style={{ animationDelay: '2s' }}></div>
      </div>
      <div className="container mx-auto px-6 md:px-12 lg:px-24 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            <GradientText text="Contact" />
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Feel free to reach out for collaborations or just a friendly hello!
          </p>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Contact Information */}
          <div className="space-y-8">
            <div>
              <h3 className="text-2xl font-bold mb-6 text-white">Get in Touch</h3>
              <p className="text-gray-300 text-lg leading-relaxed mb-8">
                I'm always excited to work on new projects and collaborate with amazing people. 
                Whether you have a question, want to discuss a project, or just want to say hello, 
                I'd love to hear from you!
              </p>
            </div>
            {/* Contact Details */}
            <div className="space-y-6">
              <div className="flex items-center space-x-4 p-4 bg-gray-800 rounded-xl backdrop-blur-sm hover:bg-gray-700 transition-all duration-300">
                <div className="bg-blue-800 p-3 rounded-full">
                  <Mail className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h4 className="font-semibold text-white">Email</h4>
                  <a
                    href={`mailto:${personalInfo.email}`}
                    className="text-blue-300 hover:text-blue-200 transition-colors"
                  >
                    {personalInfo.email}
                  </a>
                </div>
              </div>
              <div className="flex items-center space-x-4 p-4 bg-gray-800 rounded-xl backdrop-blur-sm hover:bg-gray-700 transition-all duration-300">
                <div className="bg-green-800 p-3 rounded-full">
                  <MapPin className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h4 className="font-semibold text-white">Location</h4>
                  <p className="text-gray-300">{personalInfo.location}</p>
                </div>
              </div>
            </div>
            {/* Social Links */}
            <div>
              <h4 className="text-xl font-semibold mb-4 text-white">Follow Me</h4>
              <div className="flex space-x-4">
                {socialLinks.map((link, index) => {
                  const IconComponent = getSocialIcon(link.platform)
                  return (
                    <a
                      key={link.platform}
                      href={link.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="bg-gray-800 hover:bg-gray-700 p-3 rounded-full transition-all duration-300 transform hover:scale-110 hover:rotate-12"
                    >
                      <IconComponent className="h-6 w-6 text-white" />
                    </a>
                  )
                })}
              </div>
            </div>
          </div>
          {/* Contact Form */}
            <ContactForm />
        </div>
      </div>
    </motion.section>
  )
}