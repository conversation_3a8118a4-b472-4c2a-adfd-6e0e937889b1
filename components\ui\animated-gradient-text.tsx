"use client"

import { motion } from "framer-motion"
import React from "react"

export function AnimatedGradientText({ children, className = "" }: { children: React.ReactNode, className?: string }) {
  return (
    <motion.span
      initial={{ backgroundPosition: "0% 50%" }}
      animate={{ backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"] }}
      transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
      className={
        `bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent animate-gradient font-extrabold ` + className
      }
      style={{ backgroundSize: "200% 200%" }}
    >
      {children}
    </motion.span>
  )
}
