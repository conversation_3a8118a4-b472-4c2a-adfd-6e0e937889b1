"use client"

import { useState, useEffect } from "react"
import { usePort<PERSON>lio, type GithubStats } from "@/contexts/portfolio-context"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import {
  Github,
  Star,
  GitFork,
  Users,
  BookOpen,
  Calendar,
  TrendingUp,
  RefreshCw,
  Download,
  AlertCircle,
  CheckCircle,
  Clock,
  ExternalLink
} from "lucide-react"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import type { GitHubStats, <PERSON><PERSON>HubRepo, GitH<PERSON><PERSON><PERSON> } from "@/lib/github-api"
import { formatGitHubDate, getLanguageColor } from "@/lib/github-api"

export default function GithubStatsPage() {
  const { data, updateGithubStats } = usePortfolio()
  const [username, setUsername] = useState("")
  const [githubData, setGithubData] = useState<GitHubStats | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [lastFetched, setLastFetched] = useState<string | null>(null)
  const [rateLimit, setRateLimit] = useState<any>(null)

  // Load saved username from localStorage
  useEffect(() => {
    const savedUsername = localStorage.getItem("github-username")
    if (savedUsername) {
      setUsername(savedUsername)
    }
  }, [])

  const fetchGitHubStats = async (targetUsername?: string) => {
    const usernameToFetch = targetUsername || username
    if (!usernameToFetch.trim()) {
      toast({
        title: "Username required",
        description: "Please enter a GitHub username",
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)
    setError(null)

    try {
      const response = await fetch(`/api/github/stats?username=${encodeURIComponent(usernameToFetch)}`)
      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || "Failed to fetch GitHub data")
      }

      setGithubData(result.data)
      setRateLimit(result.meta.rateLimit)
      setLastFetched(result.meta.fetchedAt)

      // Save username to localStorage
      localStorage.setItem("github-username", usernameToFetch)

      toast({
        title: "GitHub data fetched",
        description: `Successfully loaded data for ${result.data.user.login}`,
      })

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Unknown error"
      setError(errorMessage)
      toast({
        title: "Fetch failed",
        description: errorMessage,
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const saveToPortfolio = () => {
    if (!githubData) return

    const portfolioStats: GithubStats = {
      stars: githubData.totalStars,
      repos: githubData.user.public_repos,
      followers: githubData.user.followers
    }

    updateGithubStats(portfolioStats)

    toast({
      title: "GitHub stats saved",
      description: "Your GitHub statistics have been saved to your portfolio.",
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">GitHub Stats</h1>
          <p className="text-gray-400 mt-2">Fetch real GitHub statistics for your portfolio</p>
        </div>
        {rateLimit && (
          <Badge variant="outline" className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white" className="flex items-center gap-1">
            <Clock className="h-3 w-3" />
            API: {rateLimit.remaining}/{rateLimit.limit}
          </Badge>
        )}
      </div>

      {/* Fetch GitHub Data */}
      <Card className="bg-gray-900 border-gray-800 bg-gray-900 border-gray-800">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-white">
            <Github className="h-5 w-5" />
            Fetch GitHub Data
          </CardTitle>
          <CardDescription className="text-gray-400 text-gray-400">
            Enter your GitHub username to automatically fetch your real statistics
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <div className="flex-1">
              <Label htmlFor="username" className="text-gray-300">GitHub Username</Label>
              <Input
                id="username"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter your GitHub username..."
                onKeyPress={(e) => e.key === "Enter" && fetchGitHubStats()}
              />
            </div>
            <div className="flex items-end">
              <Button
                onClick={() => fetchGitHubStats()}
                disabled={isLoading || !username.trim()}
                className="min-w-[120px]"
              >
                {isLoading ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Fetching...
                  </>
                ) : (
                  <>
                    <Download className="mr-2 h-4 w-4" />
                    Fetch Data
                  </>
                )}
              </Button>
            </div>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {lastFetched && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Last fetched: {new Date(lastFetched).toLocaleString()}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* GitHub Data Display */}
      {githubData && (
        <Tabs defaultValue="overview" className="w-full bg-blue-600 hover:bg-blue-700 text-white">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="repos">Repositories</TabsTrigger>
            <TabsTrigger value="languages">Languages</TabsTrigger>
            <TabsTrigger value="save">Save to Portfolio</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            {/* User Info */}
            <Card className="bg-gray-900 border-gray-800 bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="flex items-center gap-3 text-white">
                  <img
                    src={githubData.user.avatar_url}
                    alt={githubData.user.login}
                    className="w-10 h-10 rounded-full"
                  />
                  <div>
                    <div className="flex items-center gap-2">
                      {githubData.user.name || githubData.user.login}
                      <Badge variant="outline" className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white">@{githubData.user.login}</Badge>
                    </div>
                    {githubData.user.bio && (
                      <p className="text-sm text-gray-400 mt-1">{githubData.user.bio}</p>
                    )}
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 text-2xl font-bold">
                      <Star className="h-5 w-5 text-yellow-500" />
                      {githubData.totalStars}
                    </div>
                    <p className="text-sm text-gray-400">Total Stars</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 text-2xl font-bold">
                      <BookOpen className="h-5 w-5 text-blue-500" />
                      {githubData.user.public_repos}
                    </div>
                    <p className="text-sm text-gray-400">Repositories</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 text-2xl font-bold">
                      <Users className="h-5 w-5 text-green-500" />
                      {githubData.user.followers}
                    </div>
                    <p className="text-sm text-gray-400">Followers</p>
                  </div>
                  <div className="text-center">
                    <div className="flex items-center justify-center gap-1 text-2xl font-bold">
                      <GitFork className="h-5 w-5 text-purple-500" />
                      {githubData.totalForks}
                    </div>
                    <p className="text-sm text-gray-400">Total Forks</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Most Starred Repo */}
            {githubData.mostStarredRepo && (
              <Card className="bg-gray-900 border-gray-800 bg-gray-900 border-gray-800">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2 text-white">
                    <TrendingUp className="h-5 w-5" />
                    Most Starred Repository
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="font-semibold text-lg">{githubData.mostStarredRepo.name}</h3>
                      {githubData.mostStarredRepo.description && (
                        <p className="text-gray-300 mt-1">{githubData.mostStarredRepo.description}</p>
                      )}
                      <div className="flex items-center gap-4 mt-2 text-sm text-gray-400">
                        {githubData.mostStarredRepo.language && (
                          <div className="flex items-center gap-1">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: getLanguageColor(githubData.mostStarredRepo.language) }}
                            />
                            {githubData.mostStarredRepo.language}
                          </div>
                        )}
                        <div className="flex items-center gap-1">
                          <Star className="h-3 w-3" />
                          {githubData.mostStarredRepo.stargazers_count}
                        </div>
                        <div className="flex items-center gap-1">
                          <GitFork className="h-3 w-3" />
                          {githubData.mostStarredRepo.forks_count}
                        </div>
                      </div>
                    </div>
                    <Button variant="outline" className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white" size="sm" asChild>
                      <a href={githubData.mostStarredRepo.html_url} target="_blank" rel="noopener noreferrer">
                        <ExternalLink className="h-4 w-4" />
                      </a>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="repos" className="space-y-4">
            <Card className="bg-gray-900 border-gray-800 bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white text-white">Recent Repositories</CardTitle>
                <CardDescription className="text-gray-400 text-gray-400">Your most recently updated repositories</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {githubData.recentRepos.slice(0, 10).map((repo) => (
                    <div key={repo.id} className="flex items-start justify-between p-3 border rounded-lg">
                      <div className="flex-1">
                        <h4 className="font-medium">{repo.name}</h4>
                        {repo.description && (
                          <p className="text-sm text-gray-300 mt-1">{repo.description}</p>
                        )}
                        <div className="flex items-center gap-4 mt-2 text-xs text-gray-400">
                          {repo.language && (
                            <div className="flex items-center gap-1">
                              <div
                                className="w-2 h-2 rounded-full"
                                style={{ backgroundColor: getLanguageColor(repo.language) }}
                              />
                              {repo.language}
                            </div>
                          )}
                          <div className="flex items-center gap-1">
                            <Star className="h-3 w-3" />
                            {repo.stargazers_count}
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {formatGitHubDate(repo.updated_at)}
                          </div>
                        </div>
                      </div>
                      <Button variant="ghost" size="sm" asChild>
                        <a href={repo.html_url} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="h-4 w-4" />
                        </a>
                      </Button>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="languages" className="space-y-4">
            <Card className="bg-gray-900 border-gray-800 bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white text-white">Top Languages</CardTitle>
                <CardDescription className="text-gray-400 text-gray-400">Languages used across your repositories</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {githubData.topLanguages.map((language) => {
                    const count = githubData.languages[language]
                    const percentage = (count / githubData.repos.length) * 100
                    return (
                      <div key={language} className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <div className="flex items-center gap-2">
                            <div
                              className="w-3 h-3 rounded-full"
                              style={{ backgroundColor: getLanguageColor(language) }}
                            />
                            {language}
                          </div>
                          <span>{count} repos ({percentage.toFixed(1)}%)</span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                      </div>
                    )
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="save" className="space-y-4">
            <Card className="bg-gray-900 border-gray-800 bg-gray-900 border-gray-800">
              <CardHeader>
                <CardTitle className="text-white text-white">Save to Portfolio</CardTitle>
                <CardDescription className="text-gray-400 text-gray-400">
                  Save these GitHub statistics to your portfolio
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{githubData.totalStars}</div>
                    <div className="text-sm text-gray-400">Stars</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{githubData.user.public_repos}</div>
                    <div className="text-sm text-gray-400">Repositories</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-white">{githubData.user.followers}</div>
                    <div className="text-sm text-gray-400">Followers</div>
                  </div>
                </div>

                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertDescription>
                    These statistics will be saved to your portfolio and displayed on your homepage.
                  </AlertDescription>
                </Alert>
              </CardContent>
              <CardFooter>
                <Button onClick={saveToPortfolio} className="w-full bg-blue-600 hover:bg-blue-700 text-white">
                  <Download className="mr-2 h-4 w-4" />
                  Save GitHub Stats to Portfolio
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        </Tabs>
      )}

      <Toaster />
    </div>
  )
}
