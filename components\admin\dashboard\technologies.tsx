import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

interface TechnologiesProps {
  technologies: string[];
}

export default function Technologies({ technologies }: TechnologiesProps) {
  return (
    <>
      <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
        <span className="text-sm font-medium text-gray-300">
          Total Technologies
        </span>
        <span className="text-lg font-bold text-green-400">
          {technologies.length}
        </span>
      </div>
      <div className="space-y-2">
        <span className="text-sm font-medium text-gray-300">
          Recent Technologies
        </span>
        <div className="flex flex-wrap gap-2">
          {technologies.slice(0, 6).map((tech) => (
            <span
              key={tech}
              className="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-xs font-medium border border-green-500/30"
            >
              {tech}
            </span>
          ))}
          {technologies.length > 6 && (
            <span className="px-3 py-1 bg-gray-700 text-gray-300 rounded-full text-xs font-medium">
              +{technologies.length - 6} more
            </span>
          )}
        </div>
      </div>
      <Button
        asChild
        className="w-full mt-4 bg-green-600 hover:bg-green-700 text-white border-0"
        size="sm"
      >
        <Link href="/admin/technologies">
          <span className="flex items-center justify-center gap-2">
            Manage Tech Stack <ArrowRight className="h-4 w-4" />
          </span>
        </Link>
      </Button>
    </>
  );
}
