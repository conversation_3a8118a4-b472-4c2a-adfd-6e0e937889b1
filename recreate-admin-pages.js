const fs = require('fs');

// Delete all broken admin pages
const brokenPages = [
  'app/admin/gallery/page.tsx',
  'app/admin/social-links/page.tsx',
  'app/admin/github-stats/page.tsx',
  'app/admin/skills/page.tsx',
  'app/admin/services/page.tsx'
];

brokenPages.forEach(file => {
  if (fs.existsSync(file)) {
    fs.unlinkSync(file);
    console.log(`Deleted: ${file}`);
  }
});

console.log('All broken admin pages deleted. Ready to recreate.');
