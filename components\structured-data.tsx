import Script from 'next/script'
import portfolioData from '@/data/portfolio.json'

export default function StructuredData() {
  const { personalInfo, socialLinks, skills } = portfolioData

  const personSchema = {
    "@context": "https://schema.org",
    "@type": "Person",
    "name": personalInfo.name,
    "jobTitle": personalInfo.role,
    "description": personalInfo.bio,
    "url": personalInfo.website,
    "email": personalInfo.email,
    "address": {
      "@type": "PostalAddress",
      "addressLocality": personalInfo.location
    },
    "image": personalInfo.profileImage,
    "sameAs": socialLinks.map(link => link.url),
    "knowsAbout": skills.map(skill => skill.name),
    "alumniOf": {
      "@type": "EducationalOrganization",
      "name": "UIT - VNUHCM"
    },
    "worksFor": {
      "@type": "Organization",
      "name": "Creative Studio"
    }
  }

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": `${personalInfo.name} - Portfolio`,
    "description": "Personal portfolio website showcasing web development projects and skills",
    "url": personalInfo.website,
    "author": {
      "@type": "Person",
      "name": personalInfo.name
    },
    "inLanguage": "en-US",
    "copyrightYear": new Date().getFullYear(),
    "genre": "Portfolio"
  }

  const professionalServiceSchema = {
    "@context": "https://schema.org",
    "@type": "ProfessionalService",
    "name": `${personalInfo.name} - Software Development Services`,
    "description": "Professional web and mobile development services",
    "provider": {
      "@type": "Person",
      "name": personalInfo.name,
      "jobTitle": personalInfo.role
    },
    "areaServed": "Worldwide",
    "serviceType": [
      "Web Development",
      "Mobile Development", 
      "UI/UX Design",
      "Frontend Development",
      "Backend Development"
    ],
    "url": personalInfo.website
  }

  return (
    <>
      <Script
        id="person-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(personSchema),
        }}
      />
      <Script
        id="website-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema),
        }}
      />
      <Script
        id="professional-service-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(professionalServiceSchema),
        }}
      />
    </>
  )
}
