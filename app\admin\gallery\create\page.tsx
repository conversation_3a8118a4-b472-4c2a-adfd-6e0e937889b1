"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Plus, Loader2 } from "lucide-react"
import { createGalleryItem } from "@/actions/portfolio-actions"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import LocalImageUpload from "@/components/local-image-upload"

export default function GalleryCreatePage() {
  const router = useRouter()
  const [title, setTitle] = useState("")
  const [description, setDescription] = useState("")
  const [category, setCategory] = useState("")
  const [tags, setTags] = useState("")
  const [imageUrl, setImageUrl] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")

  const handleImageUploaded = (url: string) => {
    setImageUrl(url)
  }

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault()
    setLoading(true)
    setError("")
    try {
      await createGalleryItem({
        title,
        description,
        category,
        tags: tags.split(",").map(t => t.trim()).filter(Boolean),
        imageUrl: imageUrl || "/placeholder.svg?height=400&width=600",
      })
      router.push("/admin/gallery")
    } catch (err: any) {
      setError(err.message || "Failed to create gallery item.")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="max-w-xl mx-auto py-12">
      <h1 className="text-3xl font-bold mb-6 flex items-center gap-2">
        <Plus className="w-7 h-7" />
        Create New Gallery Item
      </h1>
      <form onSubmit={handleSubmit} className="space-y-6 bg-white rounded-xl shadow-lg p-8">
        <div>
          <label className="block font-medium mb-1">Title</label>
          <Input value={title} onChange={e => setTitle(e.target.value)} required />
        </div>
        <div>
          <label className="block font-medium mb-1">Description</label>
          <Textarea value={description} onChange={e => setDescription(e.target.value)} required rows={3} />
        </div>
        <div>
          <label className="block font-medium mb-1">Category</label>
          <Input value={category} onChange={e => setCategory(e.target.value)} required />
        </div>
        <div>
          <label className="block font-medium mb-1">Tags (comma separated)</label>
          <Input value={tags} onChange={e => setTags(e.target.value)} />
        </div>
        <div>
          <label className="block font-medium mb-1">Image</label>
          <LocalImageUpload onImageUploaded={handleImageUploaded} currentImageUrl={imageUrl} />
        </div>
        {error && <div className="text-red-600 text-sm">{error}</div>}
        <Button type="submit" disabled={loading} className="w-full flex items-center justify-center gap-2">
          {loading && <Loader2 className="w-4 h-4 animate-spin" />} Create
        </Button>
      </form>
    </div>
  )
}