"use client"

import { useState } from "react"
import { motion } from "framer-motion"
import { Send, User, Mail, MessageSquare, Loader2, CheckCircle } from "lucide-react"
import { cn } from "@/lib/utils"

interface FormData {
  name: string
  email: string
  message: string
}

interface EnhancedContactFormProps {
  onSubmit: (data: FormData) => Promise<{ success: boolean; message?: string }>
  className?: string
}

export default function EnhancedContactForm({ onSubmit, className }: EnhancedContactFormProps) {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    email: "",
    message: ""
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSuccess, setIsSuccess] = useState(false)
  const [error, setError] = useState("")

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
    setError("")
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    setError("")

    try {
      const result = await onSubmit(formData)
      if (result.success) {
        setIsSuccess(true)
        setFormData({ name: "", email: "", message: "" })
        setTimeout(() => setIsSuccess(false), 3000)
      } else {
        setError(result.message || "Failed to send message")
      }
    } catch (err) {
      setError("An unexpected error occurred")
    } finally {
      setIsSubmitting(false)
    }
  }

  const inputVariants = {
    focus: { scale: 1.02, transition: { duration: 0.2 } },
    blur: { scale: 1, transition: { duration: 0.2 } }
  }

  return (
    <motion.form
      onSubmit={handleSubmit}
      className={cn("space-y-6", className)}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Name Field */}
      <motion.div className="space-y-2" variants={inputVariants}>
        <label htmlFor="name" className="block text-sm font-medium text-gray-300">
          <User className="inline w-4 h-4 mr-2" />
          Name
        </label>
        <motion.input
          type="text"
          id="name"
          name="name"
          value={formData.name}
          onChange={handleChange}
          required
          whileFocus="focus"
          whileBlur="blur"
          className={cn(
            "w-full px-4 py-3 rounded-lg border border-gray-700",
            "bg-gray-800/50 text-white placeholder-gray-400",
            "focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20",
            "transition-all duration-200 backdrop-blur-sm",
            "hover:border-gray-600"
          )}
          placeholder="Your full name"
        />
      </motion.div>

      {/* Email Field */}
      <motion.div className="space-y-2" variants={inputVariants}>
        <label htmlFor="email" className="block text-sm font-medium text-gray-300">
          <Mail className="inline w-4 h-4 mr-2" />
          Email
        </label>
        <motion.input
          type="email"
          id="email"
          name="email"
          value={formData.email}
          onChange={handleChange}
          required
          whileFocus="focus"
          whileBlur="blur"
          className={cn(
            "w-full px-4 py-3 rounded-lg border border-gray-700",
            "bg-gray-800/50 text-white placeholder-gray-400",
            "focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20",
            "transition-all duration-200 backdrop-blur-sm",
            "hover:border-gray-600"
          )}
          placeholder="<EMAIL>"
        />
      </motion.div>

      {/* Message Field */}
      <motion.div className="space-y-2" variants={inputVariants}>
        <label htmlFor="message" className="block text-sm font-medium text-gray-300">
          <MessageSquare className="inline w-4 h-4 mr-2" />
          Message
        </label>
        <motion.textarea
          id="message"
          name="message"
          value={formData.message}
          onChange={handleChange}
          required
          rows={5}
          whileFocus="focus"
          whileBlur="blur"
          className={cn(
            "w-full px-4 py-3 rounded-lg border border-gray-700",
            "bg-gray-800/50 text-white placeholder-gray-400",
            "focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20",
            "transition-all duration-200 backdrop-blur-sm resize-none",
            "hover:border-gray-600"
          )}
          placeholder="Tell me about your project or just say hello..."
        />
      </motion.div>

      {/* Error Message */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="p-3 rounded-lg bg-red-900/50 border border-red-700 text-red-300 text-sm"
        >
          {error}
        </motion.div>
      )}

      {/* Submit Button */}
      <motion.button
        type="submit"
        disabled={isSubmitting || isSuccess}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
        className={cn(
          "w-full py-3 px-6 rounded-lg font-medium",
          "bg-gradient-to-r from-blue-600 to-purple-600",
          "hover:from-blue-700 hover:to-purple-700",
          "text-white shadow-lg hover:shadow-xl",
          "transition-all duration-300",
          "disabled:opacity-50 disabled:cursor-not-allowed",
          "flex items-center justify-center gap-2",
          isSuccess && "bg-green-600 hover:bg-green-600"
        )}
      >
        {isSubmitting ? (
          <>
            <Loader2 className="w-4 h-4 animate-spin" />
            Sending...
          </>
        ) : isSuccess ? (
          <>
            <CheckCircle className="w-4 h-4" />
            Message Sent!
          </>
        ) : (
          <>
            <Send className="w-4 h-4" />
            Send Message
          </>
        )}
      </motion.button>

      {/* Success Message */}
      {isSuccess && (
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          className="p-4 rounded-lg bg-green-900/50 border border-green-700 text-green-300 text-center"
        >
          <CheckCircle className="w-6 h-6 mx-auto mb-2" />
          <p className="font-medium">Thank you for your message!</p>
          <p className="text-sm opacity-90">I'll get back to you as soon as possible.</p>
        </motion.div>
      )}
    </motion.form>
  )
}

// Character counter component
interface CharacterCounterProps {
  current: number
  max: number
  className?: string
}

export function CharacterCounter({ current, max, className }: CharacterCounterProps) {
  const percentage = (current / max) * 100
  const isNearLimit = percentage > 80
  const isOverLimit = current > max

  return (
    <div className={cn("text-xs flex items-center gap-2", className)}>
      <span className={cn(
        "font-medium",
        isOverLimit ? "text-red-400" : isNearLimit ? "text-yellow-400" : "text-gray-400"
      )}>
        {current}/{max}
      </span>
      <div className="flex-1 h-1 bg-gray-700 rounded-full overflow-hidden">
        <motion.div
          className={cn(
            "h-full rounded-full transition-colors",
            isOverLimit ? "bg-red-500" : isNearLimit ? "bg-yellow-500" : "bg-blue-500"
          )}
          initial={{ width: 0 }}
          animate={{ width: `${Math.min(percentage, 100)}%` }}
          transition={{ duration: 0.3 }}
        />
      </div>
    </div>
  )
}
