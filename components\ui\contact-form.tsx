"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Loader2, Send, ServerCrash, CheckCircle } from "lucide-react"

export default function ContactForm() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const [success, setSuccess] = useState("")
  const formRef = useRef<HTMLFormElement>(null)

  async function handleSubmit(e: React.FormEvent<HTMLFormElement>) {
    e.preventDefault()
    setLoading(true)
    setError("")
    setSuccess("")

    const formData = new FormData(e.currentTarget)
    const name = formData.get("name") as string
    const email = formData.get("email") as string
    const message = formData.get("message") as string

    try {
      const res = await fetch("/api/send-message", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name, email, message })
      })
      const data = await res.json()
      if (!res.ok) throw new Error(data.error || "Failed to send message.")
      setSuccess("Message sent successfully! I'll get back to you soon.")
      formRef.current?.reset()
    } catch (err: any) {
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  return (
    <Card className="w-full max-w-lg mx-auto bg-[#232946] border border-blue-700/40 text-blue-100 shadow-xl rounded-3xl overflow-hidden">
      <form ref={formRef} onSubmit={handleSubmit} className="space-y-6 p-8">
        <CardHeader className="space-y-2 text-center">
          <CardTitle className="text-2xl font-bold flex items-center justify-center gap-2">
            <Send className="h-6 w-6 text-blue-400" />
            Send a Message
          </CardTitle>
          <CardDescription className="text-blue-200">
            Fill out the form below and I'll get back to you as soon as possible.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-5">
          <div className="space-y-2">
            <Label htmlFor="name" className="text-blue-200">Name</Label>
            <Input id="name" name="name" required placeholder="Your name" autoComplete="name" className="bg-[#181c2b] border-blue-700/30 text-blue-100 placeholder:text-blue-300 focus:border-blue-500" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email" className="text-blue-200">Email</Label>
            <Input id="email" name="email" type="email" required placeholder="<EMAIL>" autoComplete="email" className="bg-[#181c2b] border-blue-700/30 text-blue-100 placeholder:text-blue-300 focus:border-blue-500" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="message" className="text-blue-200">Message</Label>
            <Textarea id="message" name="message" required rows={5} placeholder="How can I help you today?" className="bg-[#181c2b] border-blue-700/30 text-blue-100 placeholder:text-blue-300 focus:border-blue-500" />
          </div>
        </CardContent>
        <CardFooter className="flex flex-col gap-3">
          {error && (
            <div className="w-full text-center text-sm font-semibold text-red-400 bg-red-900/30 border border-red-700/30 rounded-md py-2 px-3 flex items-center justify-center gap-2">
              <ServerCrash className="h-4 w-4" />
              {error}
            </div>
          )}
          {success && (
            <div className="w-full text-center text-sm font-semibold text-green-300 bg-green-900/30 border border-green-700/30 rounded-md py-2 px-3 flex items-center justify-center gap-2">
              <CheckCircle className="h-4 w-4" />
              {success}
            </div>
          )}
          <Button type="submit" className="w-full flex items-center justify-center gap-2 text-base font-semibold bg-blue-800 hover:bg-blue-700 text-blue-100 border border-blue-700/40 shadow-blue-700/30 shadow-lg" disabled={loading}>
            {loading ? (
              <>
                <Loader2 className="mr-2 h-5 w-5 animate-spin" /> Sending...
              </>
            ) : (
              <>
                <Send className="mr-2 h-5 w-5" /> Send Message
              </>
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  )
}
