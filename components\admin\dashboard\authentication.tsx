import { User } from "@/contexts/auth-context";

interface PortfolioProps {
    user: User
}

export default function Authentication({ user }: PortfolioProps) {
  return (
    <div className="space-y-3">
      <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
        <span className="text-sm font-medium text-gray-300">Email</span>
        <span className="text-sm text-white font-mono">{user?.email}</span>
      </div>
      <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
        <span className="text-sm font-medium text-gray-300">User ID</span>
        <span className="text-xs font-mono text-gray-400 bg-gray-700 px-2 py-1 rounded">
          {user?.id?.slice(0, 8)}...
        </span>
      </div>
      <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
        <span className="text-sm font-medium text-gray-300">Status</span>
        <div className="flex items-center gap-2">
          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
          <span className="text-sm text-green-400 font-medium">
            Authenticated
          </span>
        </div>
      </div>
    </div>
  );
}
