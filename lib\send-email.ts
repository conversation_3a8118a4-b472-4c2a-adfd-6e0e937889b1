import { Resend } from 'resend'
import { config } from '@/lib/config'

const resend = new Resend(config.email.resendApiKey)

// Simple in-memory rate limiter
let lastSent = 0
const RATE_LIMIT_MS = 60 * 1000 // 1 email per minute (adjust as needed)

export async function sendEmail({ to, subject, html }: { to: string; subject: string; html: string }) {
  const now = Date.now()
  if (now - lastSent < RATE_LIMIT_MS) {
    throw new Error('Rate limit exceeded. Please wait before sending another message.')
  }
  lastSent = now
  const { error } = await resend.emails.send({
    from: '<EMAIL>',
    to,
    subject,
    html,
  })
  if (error) throw error
  return { success: true }
}
