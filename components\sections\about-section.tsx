import dynamic from "next/dynamic"
import type { PersonalInfo } from "./about-section-client"

interface AboutSectionProps {
  personalInfo: PersonalInfo
  technologies: string[]
  skills: { name: string; percentage: number; color?: string }[]
}

const AboutSectionClient = dynamic(() => import("./about-section-client"), { ssr: true })

export default function AboutSection({ personalInfo, technologies, skills }: AboutSectionProps) {
  return <AboutSectionClient personalInfo={personalInfo} technologies={technologies} skills={skills} />
}