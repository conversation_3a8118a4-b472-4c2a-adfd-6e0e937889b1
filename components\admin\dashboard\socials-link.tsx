import { SocialLink } from "@/actions/portfolio-actions";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

interface SocialLinksProps {
  socialLinks: SocialLink[];
}

export default function SocialLinks({ socialLinks }: SocialLinksProps) {
  return (
    <>
      <div className="space-y-3">
        <div className="flex justify-between items-center p-3 bg-gray-800/50 rounded-lg">
          <span className="text-sm font-medium text-gray-300">Total</span>
          <span className="text-sm text-white">{socialLinks.length} links</span>
        </div>
        <ul className="mt-2 space-y-1 text-white">
          {socialLinks.map((link) => (
            <li key={link.platform} className="text-sm truncate">
              • {link.platform}
            </li>
          ))}
        </ul>
      </div>
      <Button
        asChild
        className="w-full mt-4 bg-pink-600 hover:bg-pink-700 text-white border-0"
        size="sm"
      >
        <Link href="/admin/social-links">
          Edit <ArrowRight className="ml-2 h-4 w-4" />
        </Link>
      </Button>
    </>
  );
}
