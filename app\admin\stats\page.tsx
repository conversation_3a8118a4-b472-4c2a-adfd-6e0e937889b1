"use client"

import { useState, useEffect } from "react"
import { usePortfolio, type Stat } from "@/contexts/portfolio-context"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { Plus, Trash2, Edit, Save, X, Award, Coffee, Heart, Zap, TrendingUp, Users } from "lucide-react"

const iconOptions = [
  { value: "Award", label: "Award", icon: Award },
  { value: "Coffee", label: "Coffee", icon: Coffee },
  { value: "Heart", label: "Heart", icon: Heart },
  { value: "Zap", label: "Zap", icon: Zap },
  { value: "TrendingUp", label: "Trending Up", icon: TrendingUp },
  { value: "Users", label: "Users", icon: Users },
]

const colorOptions = [
  { value: "text-blue-600", label: "Blue" },
  { value: "text-amber-600", label: "Amber" },
  { value: "text-red-600", label: "Red" },
  { value: "text-green-600", label: "Green" },
  { value: "text-purple-600", label: "Purple" },
  { value: "text-pink-600", label: "Pink" },
]

export default function StatsPage() {
  const { data, updateStats } = usePortfolio()
  const [stats, setStats] = useState<Stat[]>(data.stats || [])
  const [editingId, setEditingId] = useState<number | null>(null)
  const [newStat, setNewStat] = useState({
    icon: "Award",
    label: "",
    value: "",
    color: "text-blue-600"
  })

  // Update local stats when data changes
  useEffect(() => {
    setStats(data.stats || [])
  }, [data.stats])

  const handleAddStat = async () => {
    if (!newStat.label.trim() || !newStat.value.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    const newStatItem: Stat = {
      id: Date.now(),
      icon: newStat.icon,
      label: newStat.label,
      value: newStat.value,
      color: newStat.color
    }

    const updatedStats = [...stats, newStatItem]
    setStats(updatedStats)
    setNewStat({ icon: "Award", label: "", value: "", color: "text-blue-600" })

    // Auto-save after adding
    try {
      await updateStats(updatedStats)
      toast({
        title: "Stat added",
        description: "Your stat has been added and saved successfully.",
      })
    } catch (error) {
      console.error("Error saving stat:", error)
      toast({
        title: "Error",
        description: "Stat added but failed to save. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleEditStat = (id: number) => {
    setEditingId(id)
  }

  const handleSaveStat = async (id: number, updatedStat: Partial<Stat>) => {
    const updatedStats = stats.map(stat => 
      stat.id === id ? { ...stat, ...updatedStat } : stat
    )
    setStats(updatedStats)
    setEditingId(null)

    // Auto-save after editing
    try {
      await updateStats(updatedStats)
      toast({
        title: "Stat updated",
        description: "Your stat has been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating stat:", error)
      toast({
        title: "Error",
        description: "Failed to update stat. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleDeleteStat = async (id: number) => {
    const updatedStats = stats.filter(stat => stat.id !== id)
    setStats(updatedStats)
    
    // Auto-save after deleting
    try {
      await updateStats(updatedStats)
      toast({
        title: "Stat deleted",
        description: "Your stat has been deleted successfully.",
      })
    } catch (error) {
      console.error("Error deleting stat:", error)
      toast({
        title: "Error",
        description: "Failed to delete stat. Please try again.",
        variant: "destructive",
      })
    }
  }

  const getIconComponent = (iconName: string) => {
    const iconOption = iconOptions.find(option => option.value === iconName)
    return iconOption ? iconOption.icon : Award
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Stats Management</h1>
          <p className="text-gray-500 mt-2">Manage the statistics displayed in the about section</p>
        </div>
      </div>

      {/* Add New Stat */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add New Stat
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="new-label">Label</Label>
              <Input
                id="new-label"
                value={newStat.label}
                onChange={(e) => setNewStat({ ...newStat, label: e.target.value })}
                placeholder="e.g., Years Experience"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-value">Value</Label>
              <Input
                id="new-value"
                value={newStat.value}
                onChange={(e) => setNewStat({ ...newStat, value: e.target.value })}
                placeholder="e.g., 3+"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="new-icon">Icon</Label>
              <select
                id="new-icon"
                value={newStat.icon}
                onChange={(e) => setNewStat({ ...newStat, icon: e.target.value })}
                className="w-full border rounded px-3 py-2"
              >
                {iconOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="new-color">Color</Label>
              <select
                id="new-color"
                value={newStat.color}
                onChange={(e) => setNewStat({ ...newStat, color: e.target.value })}
                className="w-full border rounded px-3 py-2"
              >
                {colorOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button onClick={handleAddStat}>
            <Plus className="h-4 w-4 mr-2" />
            Add Stat
          </Button>
        </CardFooter>
      </Card>

      {/* Existing Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <StatEditor
            key={stat.id}
            stat={stat}
            isEditing={editingId === stat.id}
            onEdit={() => handleEditStat(stat.id)}
            onSave={(updatedStat) => handleSaveStat(stat.id, updatedStat)}
            onDelete={() => handleDeleteStat(stat.id)}
            onCancel={() => setEditingId(null)}
            getIconComponent={getIconComponent}
          />
        ))}
      </div>

      {stats.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500">No stats yet. Add your first stat above.</p>
        </div>
      )}

      <Toaster />
    </div>
  )
}

interface StatEditorProps {
  stat: Stat
  isEditing: boolean
  onEdit: () => void
  onSave: (updatedStat: Partial<Stat>) => void
  onDelete: () => void
  onCancel: () => void
  getIconComponent: (iconName: string) => any
}

function StatEditor({ 
  stat, 
  isEditing, 
  onEdit, 
  onSave, 
  onDelete, 
  onCancel,
  getIconComponent 
}: StatEditorProps) {
  const [editData, setEditData] = useState({
    icon: stat.icon,
    label: stat.label,
    value: stat.value,
    color: stat.color
  })

  useEffect(() => {
    if (isEditing) {
      setEditData({
        icon: stat.icon,
        label: stat.label,
        value: stat.value,
        color: stat.color
      })
    }
  }, [isEditing, stat])

  const handleSave = () => {
    if (!editData.label.trim() || !editData.value.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }
    onSave(editData)
  }

  const IconComponent = getIconComponent(stat.icon)

  return (
    <Card className="text-center">
      <CardContent className="p-4">
        {isEditing ? (
          <div className="space-y-3">
            <select
              value={editData.icon}
              onChange={(e) => setEditData({ ...editData, icon: e.target.value })}
              className="w-full border rounded px-2 py-1"
            >
              {iconOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <Input
              value={editData.label}
              onChange={(e) => setEditData({ ...editData, label: e.target.value })}
              placeholder="Label"
            />
            <Input
              value={editData.value}
              onChange={(e) => setEditData({ ...editData, value: e.target.value })}
              placeholder="Value"
            />
            <select
              value={editData.color}
              onChange={(e) => setEditData({ ...editData, color: e.target.value })}
              className="w-full border rounded px-2 py-1"
            >
              {colorOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        ) : (
          <div className="p-4 bg-gray-50 rounded-xl hover:bg-gray-100 transition-colors duration-300">
            <IconComponent className={`w-8 h-8 mx-auto mb-2 ${stat.color}`} />
            <div className="text-2xl font-bold text-gray-800">{stat.value}</div>
            <div className="text-sm text-gray-600">{stat.label}</div>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="p-4 pt-0 flex gap-2 justify-center">
        {isEditing ? (
          <>
            <Button size="sm" onClick={handleSave}>
              <Save className="h-4 w-4 mr-1" />
              Save
            </Button>
            <Button size="sm" variant="outline" onClick={onCancel}>
              <X className="h-4 w-4 mr-1" />
              Cancel
            </Button>
          </>
        ) : (
          <>
            <Button size="sm" variant="outline" onClick={onEdit}>
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
            <Button size="sm" variant="destructive" onClick={onDelete}>
              <Trash2 className="h-4 w-4 mr-1" />
              Delete
            </Button>
          </>
        )}
      </CardFooter>
    </Card>
  )
}