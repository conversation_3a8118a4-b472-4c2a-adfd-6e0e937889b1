"use client"

import { useState, useEffect } from "react"
import { usePortfolio, type Stat } from "@/contexts/portfolio-context"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { Plus, Trash2, Edit, Save, X, Award, Coffee, Heart, Zap, TrendingUp, Users } from "lucide-react"

const iconOptions = [
  { value: "Award", label: "Award", icon: Award },
  { value: "Coffee", label: "Coffee", icon: Coffee },
  { value: "Heart", label: "Heart", icon: Heart },
  { value: "Zap", label: "Zap", icon: Zap },
  { value: "TrendingUp", label: "Trending Up", icon: TrendingUp },
  { value: "Users", label: "Users", icon: Users },
]

const colorOptions = [
  { value: "text-blue-600", label: "Blue" },
  { value: "text-amber-600", label: "Amber" },
  { value: "text-red-600", label: "Red" },
  { value: "text-green-600", label: "Green" },
  { value: "text-purple-600", label: "Purple" },
  { value: "text-pink-600", label: "Pink" },
]

export default function StatsPage() {
  const { data, updateStats } = usePortfolio()
  const [stats, setStats] = useState<Stat[]>(data.stats || [])
  const [editingId, setEditingId] = useState<number | null>(null)
  const [newStat, setNewStat] = useState({
    icon: "Award",
    label: "",
    value: "",
    color: "text-blue-600"
  })

  // Update local stats when data changes
  useEffect(() => {
    setStats(data.stats || [])
  }, [data.stats])

  const handleAddStat = async () => {
    if (!newStat.label.trim() || !newStat.value.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    const newStatItem: Stat = {
      id: Date.now(),
      icon: newStat.icon,
      label: newStat.label,
      value: newStat.value,
      color: newStat.color
    }

    const updatedStats = [...stats, newStatItem]
    setStats(updatedStats)
    setNewStat({ icon: "Award", label: "", value: "", color: "text-blue-600" })

    try {
      await updateStats(updatedStats)
      toast({
        title: "Stat added",
        description: "Your stat has been added successfully.",
      })
    } catch (error) {
      console.error("Error saving stat:", error)
      toast({
        title: "Error",
        description: "Failed to save stat. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleEditStat = (id: number) => {
    setEditingId(id)
  }

  const handleSaveStat = async (id: number, updatedStat: Partial<Stat>) => {
    const updatedStats = stats.map(stat => 
      stat.id === id ? { ...stat, ...updatedStat } : stat
    )
    setStats(updatedStats)
    setEditingId(null)

    try {
      await updateStats(updatedStats)
      toast({
        title: "Stat updated",
        description: "Your stat has been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating stat:", error)
      toast({
        title: "Error",
        description: "Failed to update stat. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleDeleteStat = async (id: number) => {
    const updatedStats = stats.filter(stat => stat.id !== id)
    setStats(updatedStats)

    try {
      await updateStats(updatedStats)
      toast({
        title: "Stat deleted",
        description: "Your stat has been deleted successfully.",
      })
    } catch (error) {
      console.error("Error deleting stat:", error)
      toast({
        title: "Error",
        description: "Failed to delete stat. Please try again.",
        variant: "destructive",
      })
    }
  }

  const getIconComponent = (iconName: string) => {
    const iconOption = iconOptions.find(option => option.value === iconName)
    return iconOption ? iconOption.icon : Award
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">Stats Management</h1>
          <p className="text-gray-400 mt-2">Manage the statistics displayed in the about section</p>
        </div>
      </div>

      {/* Add New Stat */}
      <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm">
        <CardHeader className="pb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-green-500/20 rounded-xl">
              <Plus className="h-6 w-6 text-green-400" />
            </div>
            <div>
              <CardTitle className="text-white text-xl">Add New Stat</CardTitle>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <Label htmlFor="new-label" className="text-gray-300 font-medium">Label</Label>
              <Input
                id="new-label"
                value={newStat.label}
                onChange={(e) => setNewStat({ ...newStat, label: e.target.value })}
                placeholder="e.g., Years Experience"
                className="bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20"
              />
            </div>
            <div className="space-y-3">
              <Label htmlFor="new-value" className="text-gray-300 font-medium">Value</Label>
              <Input
                id="new-value"
                value={newStat.value}
                onChange={(e) => setNewStat({ ...newStat, value: e.target.value })}
                placeholder="e.g., 3+"
                className="bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <Label htmlFor="new-icon" className="text-gray-300 font-medium">Icon</Label>
              <select
                id="new-icon"
                value={newStat.icon}
                onChange={(e) => setNewStat({ ...newStat, icon: e.target.value })}
                className="w-full bg-gray-800 border-gray-700 text-white rounded px-3 py-2 focus:border-blue-500 focus:ring-blue-500/20"
              >
                {iconOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
            <div className="space-y-3">
              <Label htmlFor="new-color" className="text-gray-300 font-medium">Color</Label>
              <select
                id="new-color"
                value={newStat.color}
                onChange={(e) => setNewStat({ ...newStat, color: e.target.value })}
                className="w-full bg-gray-800 border-gray-700 text-white rounded px-3 py-2 focus:border-blue-500 focus:ring-blue-500/20"
              >
                {colorOptions.map((option) => (
                  <option key={option.value} value={option.value}>
                    {option.label}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
        <CardFooter>
          <Button 
            onClick={handleAddStat}
            className="w-full bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white shadow-lg hover:shadow-xl transition-all duration-200"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Stat
          </Button>
        </CardFooter>
      </Card>

      {/* Existing Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat) => (
          <StatEditor
            key={stat.id}
            stat={stat}
            isEditing={editingId === stat.id}
            onEdit={() => handleEditStat(stat.id)}
            onSave={(updatedStat) => handleSaveStat(stat.id, updatedStat)}
            onDelete={() => handleDeleteStat(stat.id)}
            onCancel={() => setEditingId(null)}
            getIconComponent={getIconComponent}
          />
        ))}
      </div>

      {stats.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-400">No stats yet. Add your first stat above.</p>
        </div>
      )}

      <Toaster />
    </div>
  )
}

interface StatEditorProps {
  stat: Stat
  isEditing: boolean
  onEdit: () => void
  onSave: (updatedStat: Partial<Stat>) => void
  onDelete: () => void
  onCancel: () => void
  getIconComponent: (iconName: string) => any
}

function StatEditor({ stat, isEditing, onEdit, onSave, onDelete, onCancel, getIconComponent }: StatEditorProps) {
  const [editData, setEditData] = useState({
    icon: stat.icon,
    label: stat.label,
    value: stat.value,
    color: stat.color
  })

  useEffect(() => {
    if (isEditing) {
      setEditData({
        icon: stat.icon,
        label: stat.label,
        value: stat.value,
        color: stat.color
      })
    }
  }, [isEditing, stat])

  const handleSave = () => {
    if (!editData.label.trim() || !editData.value.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }
    onSave(editData)
  }

  const IconComponent = getIconComponent(stat.icon)

  return (
    <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm hover:bg-gray-900/70 transition-all duration-200">
      <CardContent className="p-6">
        {isEditing ? (
          <div className="space-y-4">
            <select
              value={editData.icon}
              onChange={(e) => setEditData({ ...editData, icon: e.target.value })}
              className="w-full bg-gray-800 border-gray-700 text-white rounded px-3 py-2"
            >
              {iconOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            <Input
              value={editData.label}
              onChange={(e) => setEditData({ ...editData, label: e.target.value })}
              placeholder="Label"
              className="bg-gray-800 border-gray-700 text-white placeholder-gray-500"
            />
            <Input
              value={editData.value}
              onChange={(e) => setEditData({ ...editData, value: e.target.value })}
              placeholder="Value"
              className="bg-gray-800 border-gray-700 text-white placeholder-gray-500"
            />
            <select
              value={editData.color}
              onChange={(e) => setEditData({ ...editData, color: e.target.value })}
              className="w-full bg-gray-800 border-gray-700 text-white rounded px-3 py-2"
            >
              {colorOptions.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
          </div>
        ) : (
          <div className="text-center p-4 bg-gray-800/50 rounded-xl hover:bg-gray-800/70 transition-colors duration-300">
            <IconComponent className={`w-8 h-8 mx-auto mb-3 ${stat.color}`} />
            <div className="text-2xl font-bold text-white mb-1">{stat.value}</div>
            <div className="text-sm text-gray-300">{stat.label}</div>
          </div>
        )}
      </CardContent>
      <CardFooter className="p-4 pt-0 flex gap-2 justify-center">
        {isEditing ? (
          <>
            <Button size="sm" onClick={handleSave} className="bg-green-600 hover:bg-green-700 text-white">
              <Save className="h-4 w-4 mr-1" />
              Save
            </Button>
            <Button size="sm" variant="outline" onClick={onCancel} className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white">
              <X className="h-4 w-4 mr-1" />
              Cancel
            </Button>
          </>
        ) : (
          <>
            <Button size="sm" variant="outline" onClick={onEdit} className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
            <Button size="sm" variant="destructive" onClick={onDelete}>
              <Trash2 className="h-4 w-4 mr-1" />
              Delete
            </Button>
          </>
        )}
      </CardFooter>
    </Card>
  )
}
