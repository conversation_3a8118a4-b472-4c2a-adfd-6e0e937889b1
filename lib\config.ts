// Environment validation helper
function getEnvVar(key: string, defaultValue?: string): string {
  const value = process.env[key]
  if (!value && !defaultValue) {
    console.warn(`Environment variable ${key} is not set`)
  }
  return value || defaultValue || ""
}

export const config = {
  app: {
    name: "Portfolio Admin",
    description: "Manage your portfolio content",
    version: "1.0.0",
    isDevelopment: process.env.NODE_ENV === "development",
    isProduction: process.env.NODE_ENV === "production",
  },
  github: {
    username: getEnvVar("GITHUB_USERNAME"),
    token: getEnvVar("GITHUB_TOKEN"),
  },
  unsplash: {
    accessKey: getEnvVar("UNSPLASH_ACCESS_KEY"),
  },
  email: {
    resendApiKey: getEnvVar("RESEND_API_KEY"),
    contactReceiver: getEnvVar("CONTACT_RECEIVER_EMAIL"),
  },
  admin: {
    username: getEnvVar("ADMIN_USERNAME", "admin"),
    password: getEnvVar("ADMIN_PASSWORD", "password"),
  },
  analytics: {
    gaId: getEnvVar("NEXT_PUBLIC_GA_MEASUREMENT_ID"),
    hotjarId: getEnvVar("NEXT_PUBLIC_HOTJAR_ID"),
  },
  sentry: {
    dsn: getEnvVar("NEXT_PUBLIC_SENTRY_DSN"),
  },
} as const

// Type-safe config access
export type Config = typeof config