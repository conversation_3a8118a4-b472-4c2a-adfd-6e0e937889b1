"use client"

import { useState } from "react"
import { usePortfolio } from "@/contexts/portfolio-context"
import { <PERSON>ton } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { Plus, Trash2, Edit, Save, X, Image, ExternalLink } from "lucide-react"
import LocalImageUpload from "@/components/local-image-upload"

interface GalleryItem {
  id: number
  title: string
  description: string
  image: string
  link?: string
}

export default function GalleryPage() {
  const { data, updateGallery } = usePortfolio()
  const [gallery, setGallery] = useState<GalleryItem[]>(data.gallery || [])
  const [editingId, setEditingId] = useState<number | null>(null)
  const [newItem, setNewItem] = useState({
    title: "",
    description: "",
    image: "",
    link: ""
  })

  const handleAddItem = async () => {
    if (!newItem.title.trim() || !newItem.description.trim() || !newItem.image) {
      toast({
        title: "Error",
        description: "Please fill in all required fields and upload an image.",
        variant: "destructive",
      })
      return
    }

    const newGalleryItem: GalleryItem = {
      id: Date.now(),
      title: newItem.title,
      description: newItem.description,
      image: newItem.image,
      link: newItem.link || undefined
    }

    const updatedGallery = [...gallery, newGalleryItem]
    setGallery(updatedGallery)
    setNewItem({ title: "", description: "", image: "", link: "" })

    try {
      await updateGallery(updatedGallery)
      toast({
        title: "Gallery item added",
        description: "Your gallery item has been added successfully.",
      })
    } catch (error) {
      console.error("Error adding gallery item:", error)
      toast({
        title: "Error",
        description: "Failed to add gallery item. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleEditItem = (id: number) => {
    setEditingId(id)
  }

  const handleSaveItem = async (id: number, updatedItem: Partial<GalleryItem>) => {
    const updatedGallery = gallery.map(item => 
      item.id === id ? { ...item, ...updatedItem } : item
    )
    setGallery(updatedGallery)
    setEditingId(null)

    try {
      await updateGallery(updatedGallery)
      toast({
        title: "Gallery item updated",
        description: "Your gallery item has been updated successfully.",
      })
    } catch (error) {
      console.error("Error updating gallery item:", error)
      toast({
        title: "Error",
        description: "Failed to update gallery item. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleDeleteItem = async (id: number) => {
    const updatedGallery = gallery.filter(item => item.id !== id)
    setGallery(updatedGallery)

    try {
      await updateGallery(updatedGallery)
      toast({
        title: "Gallery item deleted",
        description: "Your gallery item has been deleted successfully.",
      })
    } catch (error) {
      console.error("Error deleting gallery item:", error)
      toast({
        title: "Error",
        description: "Failed to delete gallery item. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleImageUploaded = (url: string) => {
    setNewItem({ ...newItem, image: url })
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-white">Gallery Management</h1>
          <p className="text-gray-400 mt-2">Manage your portfolio gallery items</p>
        </div>
      </div>

      {/* Add New Gallery Item */}
      <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm">
        <CardHeader className="pb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-purple-500/20 rounded-xl">
              <Image className="h-6 w-6 text-purple-400" />
            </div>
            <div>
              <CardTitle className="text-white text-xl">Add New Gallery Item</CardTitle>
              <CardDescription className="text-gray-400">
                Add a new item to your portfolio gallery
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <Label htmlFor="new-title" className="text-gray-300 font-medium">Title</Label>
              <Input
                id="new-title"
                value={newItem.title}
                onChange={(e) => setNewItem({ ...newItem, title: e.target.value })}
                placeholder="e.g., E-commerce Website"
                className="bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20"
              />
            </div>
            <div className="space-y-3">
              <Label htmlFor="new-link" className="text-gray-300 font-medium">Link (Optional)</Label>
              <Input
                id="new-link"
                value={newItem.link}
                onChange={(e) => setNewItem({ ...newItem, link: e.target.value })}
                placeholder="https://example.com"
                className="bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20"
              />
            </div>
          </div>
          
          <div className="space-y-3">
            <Label htmlFor="new-description" className="text-gray-300 font-medium">Description</Label>
            <Textarea
              id="new-description"
              value={newItem.description}
              onChange={(e) => setNewItem({ ...newItem, description: e.target.value })}
              placeholder="Describe your project..."
              rows={3}
              className="bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20 resize-none"
            />
          </div>

          <div className="space-y-3">
            <Label className="text-gray-300 font-medium">Project Image</Label>
            <LocalImageUpload
              onImageUploaded={handleImageUploaded}
              currentImageUrl={newItem.image}
              type="gallery"
            />
          </div>

          <Button 
            onClick={handleAddItem}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-700 hover:from-purple-700 hover:to-purple-800 text-white shadow-lg hover:shadow-xl transition-all duration-200"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Gallery Item
          </Button>
        </CardContent>
      </Card>

      {/* Existing Gallery Items */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {gallery.map((item) => (
          <GalleryItemCard
            key={item.id}
            item={item}
            isEditing={editingId === item.id}
            onEdit={() => handleEditItem(item.id)}
            onSave={(updatedItem) => handleSaveItem(item.id, updatedItem)}
            onDelete={() => handleDeleteItem(item.id)}
            onCancel={() => setEditingId(null)}
          />
        ))}
      </div>

      {gallery.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-400">No gallery items yet. Add your first item above.</p>
        </div>
      )}

      <Toaster />
    </div>
  )
}

interface GalleryItemCardProps {
  item: GalleryItem
  isEditing: boolean
  onEdit: () => void
  onSave: (updatedItem: Partial<GalleryItem>) => void
  onDelete: () => void
  onCancel: () => void
}

function GalleryItemCard({ item, isEditing, onEdit, onSave, onDelete, onCancel }: GalleryItemCardProps) {
  const [editData, setEditData] = useState({
    title: item.title,
    description: item.description,
    link: item.link || ""
  })

  const handleSave = () => {
    if (!editData.title.trim() || !editData.description.trim()) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }
    onSave(editData)
  }

  return (
    <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm hover:bg-gray-900/70 transition-all duration-200">
      <CardContent className="p-6">
        {isEditing ? (
          <div className="space-y-4">
            <Input
              value={editData.title}
              onChange={(e) => setEditData({ ...editData, title: e.target.value })}
              placeholder="Title"
              className="bg-gray-800 border-gray-700 text-white placeholder-gray-500"
            />
            <Textarea
              value={editData.description}
              onChange={(e) => setEditData({ ...editData, description: e.target.value })}
              placeholder="Description"
              rows={3}
              className="bg-gray-800 border-gray-700 text-white placeholder-gray-500 resize-none"
            />
            <Input
              value={editData.link}
              onChange={(e) => setEditData({ ...editData, link: e.target.value })}
              placeholder="Link (optional)"
              className="bg-gray-800 border-gray-700 text-white placeholder-gray-500"
            />
          </div>
        ) : (
          <div className="space-y-4">
            <div className="aspect-video bg-gray-800 rounded-lg overflow-hidden">
              <img 
                src={item.image} 
                alt={item.title}
                className="w-full h-full object-cover"
              />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-white mb-2">{item.title}</h3>
              <p className="text-gray-400 text-sm mb-3">{item.description}</p>
              {item.link && (
                <a 
                  href={item.link} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="inline-flex items-center text-blue-400 hover:text-blue-300 text-sm"
                >
                  <ExternalLink className="h-4 w-4 mr-1" />
                  View Project
                </a>
              )}
            </div>
          </div>
        )}
      </CardContent>
      <div className="p-4 pt-0 flex gap-2 justify-center">
        {isEditing ? (
          <>
            <Button size="sm" onClick={handleSave} className="bg-green-600 hover:bg-green-700 text-white">
              <Save className="h-4 w-4 mr-1" />
              Save
            </Button>
            <Button size="sm" variant="outline" onClick={onCancel} className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white">
              <X className="h-4 w-4 mr-1" />
              Cancel
            </Button>
          </>
        ) : (
          <>
            <Button size="sm" variant="outline" onClick={onEdit} className="border-gray-700 text-gray-300 hover:bg-gray-800 hover:text-white">
              <Edit className="h-4 w-4 mr-1" />
              Edit
            </Button>
            <Button size="sm" variant="destructive" onClick={onDelete}>
              <Trash2 className="h-4 w-4 mr-1" />
              Delete
            </Button>
          </>
        )}
      </div>
    </Card>
  )
}
