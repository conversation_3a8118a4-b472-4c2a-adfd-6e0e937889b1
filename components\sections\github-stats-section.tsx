'use client'
import dynamic from "next/dynamic"

// L<PERSON><PERSON> ý: KHÔNG import type từ file client ở file server, hãy định nghĩa lại type local hoặc dùng 'any' nếu cần tránh lỗi.
type GithubStats = {
  stars: number;
  repos: number;
  followers: number;
}

interface GithubStatsSectionProps {
  githubStats: GithubStats
}

// Dynamic import không cần type, chỉ cần truyền props đúng
// Đảm bảo dynamic import đúng kiểu cho props
// Sử dụng dynamic mà không truyền generic, chỉ cần truyền props đúng, Next.js sẽ tự nhận kiểu
const GithubStatsSectionClient = dynamic(
  () => import("./github-stats-section-client"),
  { ssr: false }
)

export default function GithubStatsSection(props: GithubStatsSectionProps) {
  return <GithubStatsSectionClient {...props} />
}
