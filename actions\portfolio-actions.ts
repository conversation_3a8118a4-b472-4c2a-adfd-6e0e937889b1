"use server"

import { revalidatePath } from "next/cache"
import { readPortfolioData, writePortfolioData, updatePortfolioSection } from "@/utils/json-storage"

export type PersonalInfo = {
  name: string
  role: string
  bio: string
  location: string
  website: string
  email: string
  profileImage?: string
}

export type GalleryItem = {
  id: string
  title: string
  description: string
  imageUrl: string
  category: string
  tags: string[]
}

export type SocialLink = {
  id?: string
  platform: string
  url: string
}

export type GithubStats = {
  stars: number
  repos: number
  followers: number
}

export type Skill = {
  name: string
  percentage: number
  color?: string
}

export type TimelineItem = {
  id: number
  type: "work" | "education"
  title: string
  org: string
  time: string
  description: string
}

export type ServiceCard = {
  id: number
  image: string
  title: string
  description: string
  category: string
}

export type Stat = {
  id: number
  icon: string
  label: string
  value: string
  color: string
}

export type PortfolioData = {
  personalInfo: PersonalInfo
  technologies: string[]
  gallery: GalleryItem[]
  socialLinks: SocialLink[]
  githubStats: GithubStats
  aboutMe: string
  workAreas: string[]
  skills: Skill[]
  timeline: TimelineItem[]
  serviceCards: ServiceCard[]
  stats: Stat[]
}

// Fetch all portfolio data
export async function fetchPortfolioData() {
  try {
    return await readPortfolioData()
  } catch (error) {
    console.error("Error fetching portfolio data:", error)
    throw error
  }
}

// Update personal info
export async function updatePersonalInfo(info: PersonalInfo) {
  try {
    await updatePortfolioSection('personalInfo', info)
    revalidatePath("/")
    revalidatePath("/admin/personal-info")
    revalidatePath("/admin/dashboard")
    return { success: true }
  } catch (error) {
    console.error("Error in updatePersonalInfo:", error)
    return { success: false, error: "Failed to update personal info" }
  }
}

// Update technologies
export async function updateTechnologies(techs: string[]) {
  try {
    await updatePortfolioSection('technologies', techs)
    revalidatePath("/")
    return { success: true }
  } catch (error) {
    console.error("Error in updateTechnologies:", error)
    return { success: false, error: "Failed to update technologies" }
  }
}

// Update about me
export async function updateAboutMe(aboutMe: string) {
  try {
    await updatePortfolioSection('aboutMe', aboutMe)
    revalidatePath("/")
    revalidatePath("/admin/personal-info")
    return { success: true }
  } catch (error) {
    console.error("Error in updateAboutMe:", error)
    return { success: false, error: "Failed to update about me" }
  }
}

// Update work areas
export async function updateWorkAreas(workAreas: string[]) {
  try {
    await updatePortfolioSection('workAreas', workAreas)
    revalidatePath("/")
    revalidatePath("/admin/personal-info")
    return { success: true }
  } catch (error) {
    console.error("Error in updateWorkAreas:", error)
    return { success: false, error: "Failed to update work areas" }
  }
}

// Update timeline
export async function updateTimeline(timeline: TimelineItem[]) {
  try {
    await updatePortfolioSection('timeline', timeline)
    revalidatePath("/")
    revalidatePath("/admin/timeline")
    return { success: true }
  } catch (error) {
    console.error("Error in updateTimeline:", error)
    return { success: false, error: "Failed to update timeline" }
  }
}

// Update service cards
export async function updateServiceCards(serviceCards: ServiceCard[]) {
  try {
    await updatePortfolioSection('serviceCards', serviceCards)
    revalidatePath("/")
    revalidatePath("/admin/services")
    return { success: true }
  } catch (error) {
    console.error("Error in updateServiceCards:", error)
    return { success: false, error: "Failed to update service cards" }
  }
}

// Update stats
export async function updateStats(stats: Stat[]) {
  try {
    await updatePortfolioSection('stats', stats)
    revalidatePath("/")
    revalidatePath("/admin/stats")
    return { success: true }
  } catch (error) {
    console.error("Error in updateStats:", error)
    return { success: false, error: "Failed to update stats" }
  }
}

// Gallery Actions
export async function createGalleryItem(item: Omit<GalleryItem, "id">) {
  try {
    const data = await readPortfolioData()
    const newItem: GalleryItem = {
      ...item,
      id: `gallery-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }
    
    if (!data.gallery) {
      data.gallery = []
    }
    
    data.gallery.push(newItem)
    await writePortfolioData(data)
    
    revalidatePath("/")
    revalidatePath("/admin/gallery")
    return { success: true, id: newItem.id }
  } catch (error) {
    console.error("Error in createGalleryItem:", error)
    return {
      success: false,
      error: "Failed to add gallery item",
      id: null
    }
  }
}

export async function updateGalleryItem(item: GalleryItem) {
  try {
    const data = await readPortfolioData()
    if (!data.gallery) {
      return { success: false, error: "Gallery not found" }
    }
    
    const itemIndex = data.gallery.findIndex(g => g.id === item.id)
    
    if (itemIndex === -1) {
      return { success: false, error: "Gallery item not found" }
    }
    
    data.gallery[itemIndex] = item
    await writePortfolioData(data)
    
    revalidatePath("/")
    revalidatePath("/admin/gallery")
    return { success: true }
  } catch (error) {
    console.error("Error in updateGalleryItem:", error)
    return { success: false, error: "Failed to update gallery item" }
  }
}

export async function deleteGalleryItem(id: string) {
  try {
    const data = await readPortfolioData()
    if (!data.gallery) {
      return { success: false, error: "Gallery not found" }
    }
    
    const itemIndex = data.gallery.findIndex(g => g.id === id)
    
    if (itemIndex === -1) {
      return { success: false, error: "Gallery item not found" }
    }
    
    data.gallery.splice(itemIndex, 1)
    await writePortfolioData(data)
    
    revalidatePath("/")
    revalidatePath("/admin/gallery")
    return { success: true }
  } catch (error) {
    console.error("Error in deleteGalleryItem:", error)
    return { success: false, error: "Failed to delete gallery item" }
  }
}

// Update social links
export async function updateSocialLinks(links: SocialLink[]) {
  try {
    // Add IDs to links that don't have them
    const linksWithIds = links.map(link => ({
      ...link,
      id: link.id || `social-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
    }))
    
    await updatePortfolioSection('socialLinks', linksWithIds)
    revalidatePath("/")
    return { success: true }
  } catch (error) {
    console.error("Error in updateSocialLinks:", error)
    return { success: false, error: "Failed to update social links" }
  }
}

// Update GitHub stats
export async function updateGithubStats(stats: GithubStats) {
  try {
    await updatePortfolioSection('githubStats', stats)
    revalidatePath("/")
    return { success: true }
  } catch (error) {
    console.error("Error in updateGithubStats:", error)
    return { success: false, error: "Failed to update GitHub stats" }
  }
}

// Update skills
export async function updateSkills(skills: Skill[]) {
  try {
    await updatePortfolioSection('skills', skills)
    revalidatePath("/")
    revalidatePath("/admin/skills")
    return { success: true }
  } catch (error) {
    console.error("Error in updateSkills:", error)
    return { success: false, error: "Failed to update skills" }
  }
}