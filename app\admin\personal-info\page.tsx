"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { usePortfolio, type PersonalInfo } from "@/contexts/portfolio-context"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { toast } from "@/components/ui/use-toast"
import { Toaster } from "@/components/ui/toaster"
import { Loader2, User, Mail, MapPin, Globe, FileText } from "lucide-react"
import LocalImageUpload from "@/components/local-image-upload"

export default function PersonalInfoPage() {
  const { data, updatePersonalInfo, updateAboutMe, updateWorkAreas, isLoading } = usePortfolio()
  const [formData, setFormData] = useState<PersonalInfo & { profileImage: string }>({
    ...data.personalInfo,
    profileImage: data.personalInfo.profileImage || "",
  })
  const [isSaving, setIsSaving] = useState(false)
  const [workAreaInput, setWorkAreaInput] = useState("")
  const [aboutMe, setAboutMe] = useState(data.aboutMe || "")
  const [workAreas, setWorkAreas] = useState<string[]>(data.workAreas || [])

  // Update form data when context data changes
  useEffect(() => {
    setFormData({
      ...data.personalInfo,
      profileImage: data.personalInfo.profileImage || "",
    })
    setAboutMe(data.aboutMe || "")
    setWorkAreas(data.workAreas || [])
  }, [data.personalInfo, data.aboutMe, data.workAreas])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
  }

  const handleImageUploaded = (url: string) => {
    setFormData((prev) => ({ ...prev, profileImage: url }))
  }

  const handleAddWorkArea = () => {
    if (workAreaInput.trim() && !workAreas.includes(workAreaInput.trim())) {
      setWorkAreas([...workAreas, workAreaInput.trim()])
      setWorkAreaInput("")
    }
  }

  const handleRemoveWorkArea = (area: string) => {
    setWorkAreas(workAreas.filter((a) => a !== area))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)
    try {
      await updatePersonalInfo(formData)
      await updateAboutMe(aboutMe)
      await updateWorkAreas(workAreas)
      toast({
        title: "Personal information updated",
        description: "Your changes have been saved successfully.",
      })
    } catch (error) {
      console.error("Error updating personal info:", error)
      toast({
        title: "Error",
        description: "Failed to update personal information. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSaving(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-white">Personal Information</h1>
        <p className="text-gray-400 mt-2">Update your personal details</p>
      </div>

      <Card className="bg-gray-900/50 border-gray-700/50 backdrop-blur-sm">
        <form onSubmit={handleSubmit}>
          <CardHeader className="pb-6">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-blue-500/20 rounded-xl">
                <User className="h-6 w-6 text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-white text-xl">Edit Personal Information</CardTitle>
                <CardDescription className="text-gray-400 mt-1">
                  This information will be displayed on your portfolio
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="name" className="text-gray-300 font-medium flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Full Name
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleChange}
                  required
                  className="bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20"
                  placeholder="Enter your full name"
                />
              </div>
              <div className="space-y-3">
                <Label htmlFor="role" className="text-gray-300 font-medium flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Role/Title
                </Label>
                <Input
                  id="role"
                  name="role"
                  value={formData.role}
                  onChange={handleChange}
                  required
                  className="bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20"
                  placeholder="e.g. Full Stack Developer"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <Label htmlFor="email" className="text-gray-300 font-medium flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20"
                  placeholder="<EMAIL>"
                />
              </div>
              <div className="space-y-3">
                <Label htmlFor="location" className="text-gray-300 font-medium flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Location
                </Label>
                <Input
                  id="location"
                  name="location"
                  value={formData.location}
                  onChange={handleChange}
                  className="bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20"
                  placeholder="City, Country"
                />
              </div>
            </div>

            <div className="space-y-3">
              <Label htmlFor="website" className="text-gray-300 font-medium flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Website
              </Label>
              <Input
                id="website"
                name="website"
                value={formData.website}
                onChange={handleChange}
                className="bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20"
                placeholder="https://yourwebsite.com"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="bio" className="text-gray-300 font-medium">Bio</Label>
              <Textarea
                id="bio"
                name="bio"
                value={formData.bio}
                onChange={handleChange}
                rows={4}
                required
                className="bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20 resize-none"
                placeholder="Tell us about yourself..."
              />
            </div>

            <div className="space-y-3">
              <Label className="text-gray-300 font-medium">Profile Image</Label>
              <LocalImageUpload
                onImageUploaded={handleImageUploaded}
                currentImageUrl={formData.profileImage}
                type="avatar"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="aboutMe" className="text-gray-300 font-medium">About Me (detailed)</Label>
              <Textarea
                id="aboutMe"
                name="aboutMe"
                value={aboutMe}
                onChange={(e) => setAboutMe(e.target.value)}
                rows={4}
                className="bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20 resize-none"
                placeholder="Detailed description about yourself..."
              />
            </div>

            <div className="space-y-3">
              <Label className="text-gray-300 font-medium">My Work Areas</Label>
              <div className="flex gap-2 mb-2">
                <Input
                  value={workAreaInput}
                  onChange={(e) => setWorkAreaInput(e.target.value)}
                  placeholder="Add a work area"
                  className="bg-gray-800 border-gray-700 text-white placeholder-gray-500 focus:border-blue-500 focus:ring-blue-500/20"
                />
                <Button
                  type="button"
                  onClick={handleAddWorkArea}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Add
                </Button>
              </div>
              <div className="flex flex-wrap gap-2">
                {workAreas.map((area) => (
                  <span
                    key={area}
                    className="inline-flex items-center bg-blue-500/20 text-blue-300 px-3 py-1 rounded-full border border-blue-500/30"
                  >
                    {area}
                    <button
                      type="button"
                      className="ml-2 text-red-400 hover:text-red-300"
                      onClick={() => handleRemoveWorkArea(area)}
                    >
                      ×
                    </button>
                  </span>
                ))}
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              type="submit"
              disabled={isSaving}
              className="w-full bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-200"
            >
              {isSaving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </CardFooter>
        </form>
      </Card>
      <Toaster />
    </div>
  )
}
